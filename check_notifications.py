from app import create_app, db
from app.models import Notification

def check_notifications():
    app = create_app()
    with app.app_context():
        notifications = Notification.query.all()
        print('Notifications:')
        for notification in notifications:
            print(f'ID: {notification.id}, Message: {notification.message}, Date: {notification.date_creation}, Lu: {notification.lu}, User ID: {notification.user_id}')

if __name__ == '__main__':
    check_notifications()
