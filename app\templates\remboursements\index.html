{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-file-invoice-dollar me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Dossiers de Remboursement</span>
                        </h4>
                        <a href="{{ url_for('new_remboursement') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nouveau Dossier
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_remboursements') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un dossier..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Dossiers de Remboursement</h5>
        </div>
        <div class="card-body">
            {% if remboursements %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Entreprise</th>
                                <th>Organisme</th>
                                <th>Formateur</th>
                                <th>Thème</th>
                                <th>Date</th>
                                <th>Facturation</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for remboursement in remboursements %}
                                <tr>
                                    <td>{{ remboursement.id }}</td>
                                    <td>{{ remboursement.fiche_inscription.raison_sociale if remboursement.fiche_inscription else '-' }}</td>
                                    <td>{{ remboursement.organisme.raison_sociale if remboursement.organisme else '-' }}</td>
                                    <td>{{ remboursement.formateur.nom_prenom if remboursement.formateur else '-' }}</td>
                                    <td>{{ remboursement.theme }}</td>
                                    <td>{{ remboursement.date.strftime('%d/%m/%Y') if remboursement.date else '-' }}</td>
                                    <td>{{ remboursement.facturation }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_remboursement', id=remboursement.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('print_remboursement', id=remboursement.id) }}" class="btn btn-sm btn-info" title="Imprimer" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                                    data-delete-url="{{ url_for('delete_remboursement', id=remboursement.id) }}"
                                                    data-delete-message="Êtes-vous sûr de vouloir supprimer ce dossier?"
                                                    data-use-modal="true"
                                                    data-delete-title="Confirmer la suppression">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    Aucun dossier de remboursement trouvé. <a href="{{ url_for('new_remboursement') }}">Créer un nouveau dossier</a>.
                </div>
            {% endif %}
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='remboursements') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
