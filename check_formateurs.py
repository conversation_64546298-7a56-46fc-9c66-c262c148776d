from app import create_app, db
from app.models import Formateur

def check_formateurs():
    app = create_app()
    with app.app_context():
        formateurs = Formateur.query.all()
        print('Formateurs:')
        for formateur in formateurs:
            print(f'ID: {formateur.id}, Nom: {formateur.nom_prenom}, Spécialité: {formateur.specialite}, Email: {formateur.email}')
            print(f'CV: {formateur.cv}, Diplômes: {formateur.diplomes}, Ville: {formateur.ville}, Téléphone: {formateur.num_tel}')

if __name__ == '__main__':
    check_formateurs()
