import os

def find_files(directory, keyword):
    found_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if keyword in file:
                found_files.append(os.path.join(root, file))
    return found_files

# البحث عن الملفات التي تحتوي على كلمة "accueil"
accueil_files = find_files('app', 'accueil')
print("الملفات التي تحتوي على كلمة 'accueil':")
for file in accueil_files:
    print(file)

# البحث عن الملفات التي تحتوي على كلمة "formation"
formation_files = find_files('app', 'formation')
print("\nالملفات التي تحتوي على كلمة 'formation':")
for file in formation_files:
    print(file)
