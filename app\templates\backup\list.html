{% extends "base.html" %}

{% block title %}قائمة النسخ الاحتياطية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-list"></i> قائمة النسخ الاحتياطية
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> نسخة احتياطية جديدة
                            </a>
                            <a href="{{ url_for('import_database') }}" class="btn btn-warning">
                                <i class="fas fa-upload"></i> استيراد قاعدة بيانات
                            </a>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="{{ url_for('backup_settings') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-cog"></i> إعدادات النسخ الاحتياطي
                            </a>
                        </div>
                    </div>

                    {% if backups %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم النسخة</th>
                                        <th>التاريخ والوقت</th>
                                        <th>الحجم</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>الوصف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for backup in backups %}
                                    <tr>
                                        <td>
                                            <strong>{{ backup.nom_fichier }}</strong>
                                        </td>
                                        <td>
                                            <small>
                                                {{ backup.date_creation.strftime('%Y-%m-%d') }}<br>
                                                {{ backup.date_creation.strftime('%H:%M:%S') }}
                                            </small>
                                        </td>
                                        <td>
                                            {% if backup.taille_fichier %}
                                                {% if backup.taille_fichier < 1024 %}
                                                    {{ backup.taille_fichier }} B
                                                {% elif backup.taille_fichier < 1048576 %}
                                                    {{ "%.1f"|format(backup.taille_fichier / 1024) }} KB
                                                {% else %}
                                                    {{ "%.1f"|format(backup.taille_fichier / 1048576) }} MB
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.type_sauvegarde == 'manual' %}
                                                <span class="badge bg-primary">يدوي</span>
                                            {% elif backup.type_sauvegarde == 'automatic' %}
                                                <span class="badge bg-success">تلقائي</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ backup.type_sauvegarde }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.statut == 'success' %}
                                                <span class="badge bg-success">نجح</span>
                                            {% elif backup.statut == 'failed' %}
                                                <span class="badge bg-danger">فشل</span>
                                            {% elif backup.statut == 'in_progress' %}
                                                <span class="badge bg-warning">قيد التنفيذ</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ backup.statut }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.description %}
                                                {{ backup.description[:50] }}{% if backup.description|length > 50 %}...{% endif %}
                                            {% else %}
                                                <em class="text-muted">لا يوجد وصف</em>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.statut == 'success' %}
                                                <a href="{{ url_for('static', filename='backups/' + backup.nom_fichier) }}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete('{{ backup.id }}', '{{ backup.nom_fichier }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                إجمالي النسخ الاحتياطية: {{ backups|length }}
                            </small>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            لا توجد نسخ احتياطية حالياً
                            <br><br>
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> إنشاء أول نسخة احتياطية
                            </a>
                        </div>
                    {% endif %}

                    <div class="mt-4">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف النسخة الاحتياطية "<span id="backupName"></span>"؟
                <br><br>
                <strong class="text-danger">تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(backupId, backupName) {
    document.getElementById('backupName').textContent = backupName;
    document.getElementById('deleteForm').action = '/backup/delete/' + backupId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
