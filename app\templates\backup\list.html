{% extends "base.html" %}

{% block title %}Liste des Sauvegardes{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-list"></i> Liste des Sauvegardes
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Nouvelle sauvegarde
                            </a>
                            <a href="{{ url_for('import_database') }}" class="btn btn-warning">
                                <i class="fas fa-upload"></i> Importer base de données
                            </a>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="{{ url_for('backup_settings') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-cog"></i> Paramètres de sauvegarde
                            </a>
                        </div>
                    </div>

                    {% if backups %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Nom de la sauvegarde</th>
                                        <th>Date et Heure</th>
                                        <th>Taille</th>
                                        <th>Type</th>
                                        <th>État</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for backup in backups.items %}
                                    <tr>
                                        <td>
                                            <strong>{{ backup.nom_fichier }}</strong>
                                        </td>
                                        <td>
                                            <small>
                                                {{ backup.date_creation.strftime('%Y-%m-%d') }}<br>
                                                {{ backup.date_creation.strftime('%H:%M:%S') }}
                                            </small>
                                        </td>
                                        <td>
                                            {% if backup.taille_fichier %}
                                                {% if backup.taille_fichier < 1024 %}
                                                    {{ backup.taille_fichier }} B
                                                {% elif backup.taille_fichier < 1048576 %}
                                                    {{ "%.1f"|format(backup.taille_fichier / 1024) }} KB
                                                {% else %}
                                                    {{ "%.1f"|format(backup.taille_fichier / 1048576) }} MB
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">Non défini</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.type_sauvegarde == 'manual' %}
                                                <span class="badge bg-primary">Manuel</span>
                                            {% elif backup.type_sauvegarde == 'automatic' %}
                                                <span class="badge bg-success">Automatique</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ backup.type_sauvegarde }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.statut == 'success' %}
                                                <span class="badge bg-success">Réussi</span>
                                            {% elif backup.statut == 'failed' %}
                                                <span class="badge bg-danger">Échoué</span>
                                            {% elif backup.statut == 'in_progress' %}
                                                <span class="badge bg-warning">En cours</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ backup.statut }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.description %}
                                                {{ backup.description[:50] }}{% if backup.description|length > 50 %}...{% endif %}
                                            {% else %}
                                                <em class="text-muted">Aucune description</em>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.statut == 'success' %}
                                                <a href="{{ url_for('static', filename='backups/' + backup.nom_fichier) }}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete('{{ backup.id }}', '{{ backup.nom_fichier }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                Total des sauvegardes : {{ backups.total }}
                            </small>
                        </div>

                        <!-- Pagination -->
                        {% if backups.pages > 1 %}
                        <nav aria-label="Navigation des sauvegardes">
                            <ul class="pagination justify-content-center">
                                {% if backups.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('backup_list', page=backups.prev_num) }}">Précédent</a>
                                    </li>
                                {% endif %}

                                {% for page_num in backups.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != backups.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('backup_list', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if backups.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('backup_list', page=backups.next_num) }}">Suivant</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            Aucune sauvegarde disponible actuellement
                            <br><br>
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Créer la première sauvegarde
                            </a>
                        </div>
                    {% endif %}

                    <div class="mt-4">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer la sauvegarde "<span id="backupName"></span>" ?
                <br><br>
                <strong class="text-danger">Avertissement :</strong> Cette action ne peut pas être annulée.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(backupId, backupName) {
    document.getElementById('backupName').textContent = backupName;
    document.getElementById('deleteForm').action = '/backup/delete/' + backupId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
