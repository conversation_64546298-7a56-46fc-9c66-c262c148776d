{% extends "base.html" %}

{% block title %}Liste des Sauvegardes{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-database me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Liste des Sauvegardes</span>
                        </h4>
                        <a href="{{ url_for('manual_backup') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nouvelle Sauvegarde
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="{{ url_for('import_database') }}" class="btn btn-warning">
                                <i class="fas fa-upload"></i> Importer base de données
                            </a>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="{{ url_for('backup_settings') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-cog"></i> Paramètres de sauvegarde
                            </a>
                        </div>
                    </div>

                    {% if backups %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="color: black;">Nom de la sauvegarde</th>
                                        <th style="color: black;">Date et Heure</th>
                                        <th style="color: black;">Taille</th>
                                        <th style="color: black;">Type</th>
                                        <th style="color: black;">État</th>
                                        <th style="color: black;">Description</th>
                                        <th style="color: black;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for backup in backups.items %}
                                    <tr>
                                        <td>
                                            <strong>{{ backup.nom_fichier }}</strong>
                                        </td>
                                        <td>
                                            <small>
                                                {{ backup.date_creation.strftime('%Y-%m-%d') }}<br>
                                                {{ backup.date_creation.strftime('%H:%M:%S') }}
                                            </small>
                                        </td>
                                        <td>
                                            {% if backup.taille_fichier %}
                                                {% if backup.taille_fichier < 1024 %}
                                                    {{ backup.taille_fichier }} B
                                                {% elif backup.taille_fichier < 1048576 %}
                                                    {{ "%.1f"|format(backup.taille_fichier / 1024) }} KB
                                                {% else %}
                                                    {{ "%.1f"|format(backup.taille_fichier / 1048576) }} MB
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">Non défini</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.type_sauvegarde == 'manual' %}
                                                <span class="badge bg-primary">Manuel</span>
                                            {% elif backup.type_sauvegarde == 'automatic' %}
                                                <span class="badge bg-success">Automatique</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ backup.type_sauvegarde }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.statut == 'success' %}
                                                <span class="badge bg-success">Réussi</span>
                                            {% elif backup.statut == 'failed' %}
                                                <span class="badge bg-danger">Échoué</span>
                                            {% elif backup.statut == 'in_progress' %}
                                                <span class="badge bg-warning">En cours</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ backup.statut }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.description %}
                                                {{ backup.description[:50] }}{% if backup.description|length > 50 %}...{% endif %}
                                            {% else %}
                                                <em class="text-muted">Aucune description</em>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if backup.statut == 'success' %}
                                                <a href="{{ url_for('static', filename='backups/' + backup.nom_fichier) }}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="Supprimer"
                                                    data-delete-url="{{ url_for('delete_backup', id=backup.id) }}"
                                                    data-delete-message="Êtes-vous sûr de vouloir supprimer la sauvegarde '{{ backup.nom_fichier }}'?"
                                                    data-use-modal="true"
                                                    data-delete-title="Confirmer la suppression">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                Total des sauvegardes : {{ backups.total }}
                            </small>
                        </div>

                        <!-- Pagination -->
                        {% if backups.pages > 1 %}
                        <nav aria-label="Navigation des sauvegardes">
                            <ul class="pagination justify-content-center">
                                {% if backups.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('backup_list', page=backups.prev_num) }}">Précédent</a>
                                    </li>
                                {% endif %}

                                {% for page_num in backups.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != backups.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('backup_list', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if backups.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('backup_list', page=backups.next_num) }}">Suivant</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            Aucune sauvegarde disponible actuellement
                            <br><br>
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> Créer la première sauvegarde
                            </a>
                        </div>
                    {% endif %}

                    <div class="mt-4">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}
