from app import create_app, db
from app.models import User, Domaine, Theme, Organisme, Formateur, Formation
from datetime import datetime, timezone, timedelta

def add_sample_data():
    app = create_app()
    with app.app_context():
        # إضافة مدربين
        if Formateur.query.count() == 0:
            formateur1 = Formateur(
                nom_prenom='<PERSON>',
                specialite='Développement Web',
                cv=True,
                diplomes=True,
                adresse='78 Rue des Écoles',
                ville='Casablanca',
                num_tel='0661234567',
                email='<EMAIL>'
            )
            
            formateur2 = Formateur(
                nom_prenom='Fatima Zahra',
                specialite='Management et Leadership',
                cv=True,
                diplomes=True,
                adresse='15 Rue des Fleurs',
                ville='Rabat',
                num_tel='0662345678',
                email='<EMAIL>'
            )
            
            db.session.add_all([formateur1, formateur2])
            db.session.commit()
            print("Added sample formateurs")
        
        # إضافة مواضيع
        if Theme.query.count() == 0:
            domaines = Domaine.query.all()
            if domaines:
                theme1 = Theme(nom='Développement Web', domaine_id=domaines[0].id)
                theme2 = Theme(nom='Bases de données', domaine_id=domaines[0].id)
                theme3 = Theme(nom='Leadership', domaine_id=domaines[1].id)
                theme4 = Theme(nom='Gestion de projet', domaine_id=domaines[1].id)
                
                db.session.add_all([theme1, theme2, theme3, theme4])
                db.session.commit()
                print("Added sample themes")
        
        # إضافة تكوينات
        if Formation.query.count() == 0:
            organismes = Organisme.query.all()
            if organismes:
                formation1 = Formation(
                    theme='Formation en Développement Web',
                    date=datetime.now(timezone.utc).date(),
                    organisme_id=organismes[0].id
                )
                
                formation2 = Formation(
                    theme='Formation en Management',
                    date=(datetime.now(timezone.utc) + timedelta(days=7)).date(),
                    organisme_id=organismes[0].id
                )
                
                db.session.add_all([formation1, formation2])
                db.session.commit()
                print("Added sample formations")
        
        print("Sample data added successfully!")

if __name__ == '__main__':
    add_sample_data()
