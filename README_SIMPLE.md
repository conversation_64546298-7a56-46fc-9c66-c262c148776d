# Guide simplifié pour créer un exécutable de l'application Gestion des Formations

Ce guide explique la méthode la plus simple pour créer un fichier exécutable (.exe) de l'application.

## Méthode 1: Utiliser le fichier batch

1. Double-cliquez simplement sur le fichier `build_exe.bat`
2. Attendez que le processus se termine
3. Le fichier exécutable sera créé dans le dossier `dist`

## Méthode 2: Utiliser les commandes manuellement

Si la méthode 1 ne fonctionne pas, suivez ces étapes:

1. Ouvrez une invite de commande (cmd) dans le dossier du projet
2. Installez PyInstaller:
   ```
   pip install pyinstaller
   ```
3. C<PERSON>ez le fichier exécutable:
   ```
   pyinstaller --noconfirm --onefile --windowed --add-data "app/templates;app/templates" --add-data "app/static;app/static" --add-data "app/static/uploads;app/static/uploads" --add-data "migrations;migrations" --add-data "app.db;." --hidden-import email_validator --hidden-import flask --hidden-import flask_sqlalchemy --hidden-import flask_migrate --hidden-import flask_login --hidden-import flask_wtf --hidden-import wtforms --hidden-import werkzeug --hidden-import jinja2 --hidden-import sqlalchemy --hidden-import email_validator --hidden-import itsdangerous --hidden-import click --hidden-import markupsafe --name Gestion_Formation run.py
   ```

## Utilisation de l'exécutable

1. Une fois créé, le fichier exécutable se trouve dans le dossier `dist`
2. Double-cliquez sur `Gestion_Formation.exe` pour lancer l'application
3. L'application ouvrira automatiquement votre navigateur à l'adresse correcte
4. L'application sera accessible depuis n'importe quel appareil du réseau local à l'adresse http://IP_DE_VOTRE_PC:5000

## Remarques importantes

- L'application utilise le port 5000. Assurez-vous qu'il n'est pas utilisé par une autre application.
- La base de données SQLite est incluse dans l'exécutable. Toutes les données seront conservées entre les sessions.
- Pour arrêter l'application, fermez la fenêtre de console qui s'est ouverte lors du lancement.
