@echo off
echo ===== Creation du fichier executable pour Gestion des Formations =====
echo.

echo 1. Creation du fichier executable...
pyinstaller --noconfirm --onefile --windowed --name Gestion_Formation run.py
echo.

echo 2. Verification du resultat...
if exist "dist\Gestion_Formation.exe" (
    echo Le fichier executable a ete cree avec succes!
    echo Chemin: %CD%\dist\Gestion_Formation.exe
) else (
    echo ERREUR: Le fichier executable n'a pas ete cree.
    exit /b 1
)
echo.

echo ===== Processus termine =====
pause
