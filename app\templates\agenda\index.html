{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-calendar-alt me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Agenda des Formateurs</span>
                        </h4>
                        <a href="{{ url_for('new_agenda') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nouveau Rendez-vous
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-0">Calendrier des Rendez-vous</h5>
                </div>
                <div class="col-md-6 text-right">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('agenda_formateurs', month=(current_month-1) if current_month > 1 else 12, year=(current_year) if current_month > 1 else (current_year-1)) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-chevron-left"></i> Mois précédent
                        </a>
                        <a href="{{ url_for('agenda_formateurs', month=current_month, year=current_year) }}" class="btn btn-sm btn-outline-primary">
                            Aujourd'hui
                        </a>
                        <a href="{{ url_for('agenda_formateurs', month=(current_month+1) if current_month < 12 else 1, year=(current_year) if current_month < 12 else (current_year+1)) }}" class="btn btn-sm btn-outline-secondary">
                            Mois suivant <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <h4 class="text-center mb-4">{{ month_name }} {{ current_year }}</h4>

            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Lun</th>
                            <th class="text-center">Mar</th>
                            <th class="text-center">Mer</th>
                            <th class="text-center">Jeu</th>
                            <th class="text-center">Ven</th>
                            <th class="text-center">Sam</th>
                            <th class="text-center">Dim</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for week in calendar %}
                        <tr style="height: 100px;">
                            {% for day, events in week %}
                            <td class="{% if day == 0 %}bg-light{% elif day == current_day and current_month == display_month and current_year == display_year %}bg-info text-white{% endif %}" style="width: 14.28%; vertical-align: top;">
                                {% if day != 0 %}
                                <div class="d-flex justify-content-between">
                                    <span>{{ day }}</span>
                                    {% if events %}
                                    <a href="#" data-toggle="modal" data-target="#eventsModal{{ day }}">
                                        <span class="badge badge-pill badge-primary">{{ events|length }}</span>
                                    </a>
                                    {% endif %}
                                </div>

                                {% if events %}
                                <div class="small mt-1">
                                    {% for event in events[:3] %}
                                    {% set formateur_colors = ['#e3f2fd', '#f3e5f5', '#e8f5e8', '#fff3e0', '#fce4ec', '#e0f2f1', '#f1f8e9', '#fff8e1'] %}
                                    {% set color_index = event.formateur.id % formateur_colors|length %}
                                    <div class="mb-1 p-1 rounded" style="background-color: {{ formateur_colors[color_index] }}; border-left: 3px solid {{ ['#2196f3', '#9c27b0', '#4caf50', '#ff9800', '#e91e63', '#009688', '#8bc34a', '#ffc107'][color_index] }}; font-size: 0.75em;">
                                        <div class="fw-bold text-truncate" style="color: #333;" title="{{ event.formateur.nom_prenom }}">
                                            <i class="fas fa-user-tie me-1"></i>{{ event.formateur.nom_prenom }}
                                        </div>
                                        <div class="text-truncate text-muted" style="font-size: 0.9em;" title="{{ event.description }}">
                                            {{ event.description[:30] }}{% if event.description|length > 30 %}...{% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                    {% if events|length > 3 %}
                                    <div class="text-muted text-center" style="font-size: 0.7em;">+ {{ events|length - 3 }} autres...</div>
                                    {% endif %}
                                </div>

                                <!-- Modal pour afficher tous les événements du jour -->
                                <div class="modal fade" id="eventsModal{{ day }}" tabindex="-1" role="dialog" aria-labelledby="eventsModalLabel{{ day }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="eventsModalLabel{{ day }}">Rendez-vous du {{ day }}/{{ current_month }}/{{ current_year }}</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <ul class="list-group">
                                                    {% for event in events %}
                                                    <li class="list-group-item">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <strong>{{ event.formateur.nom_prenom }}</strong>
                                                                <p class="mb-0 text-muted">{{ event.description }}</p>
                                                            </div>
                                                            <div class="btn-group" role="group">
                                                                <a href="{{ url_for('edit_agenda', id=event.id) }}" class="btn btn-sm btn-warning">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                                                        data-delete-url="{{ url_for('delete_agenda', id=event.id) }}"
                                                                        data-delete-message="Êtes-vous sûr de vouloir supprimer ce rendez-vous?"
                                                                        data-use-modal="true"
                                                                        data-delete-title="Confirmer la suppression">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Fermer</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                {% endif %}
                            </td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Rendez-vous</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Formateur</th>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for agenda in agendas %}
                        <tr>
                            <td>{{ agenda.id }}</td>
                            <td>{{ agenda.formateur.nom_prenom }}</td>
                            <td>{{ agenda.date_rendezvous.strftime('%d/%m/%Y') }}</td>
                            <td>{{ agenda.description }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_agenda', id=agenda.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('print_agenda', id=agenda.id) }}" class="btn btn-sm btn-info" title="Imprimer" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                            data-delete-url="{{ url_for('delete_agenda', id=agenda.id) }}"
                                            data-delete-message="Êtes-vous sûr de vouloir supprimer ce rendez-vous avec {{ agenda.formateur.nom_prenom }} le {{ agenda.date_rendezvous.strftime('%d/%m/%Y') }}?"
                                            data-use-modal="true"
                                            data-delete-title="Confirmer la suppression">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='agenda') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
