{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-calendar-alt"></i> Agenda des Formateurs</h1>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('new_agenda') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter un Rendez-vous
            </a>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-0">Calendrier des Rendez-vous</h5>
                </div>
                <div class="col-md-6 text-right">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('agenda_formateurs', month=(current_month-1) if current_month > 1 else 12, year=(current_year) if current_month > 1 else (current_year-1)) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-chevron-left"></i> Mois précédent
                        </a>
                        <a href="{{ url_for('agenda_formateurs', month=current_month, year=current_year) }}" class="btn btn-sm btn-outline-primary">
                            Aujourd'hui
                        </a>
                        <a href="{{ url_for('agenda_formateurs', month=(current_month+1) if current_month < 12 else 1, year=(current_year) if current_month < 12 else (current_year+1)) }}" class="btn btn-sm btn-outline-secondary">
                            Mois suivant <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <h4 class="text-center mb-4">{{ month_name }} {{ current_year }}</h4>

            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Lun</th>
                            <th class="text-center">Mar</th>
                            <th class="text-center">Mer</th>
                            <th class="text-center">Jeu</th>
                            <th class="text-center">Ven</th>
                            <th class="text-center">Sam</th>
                            <th class="text-center">Dim</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for week in calendar %}
                        <tr style="height: 100px;">
                            {% for day, events in week %}
                            <td class="{% if day == 0 %}bg-light{% elif day == current_day and current_month == display_month and current_year == display_year %}bg-info text-white{% endif %}" style="width: 14.28%; vertical-align: top;">
                                {% if day != 0 %}
                                <div class="d-flex justify-content-between">
                                    <span>{{ day }}</span>
                                    {% if events %}
                                    <a href="#" data-toggle="modal" data-target="#eventsModal{{ day }}">
                                        <span class="badge badge-pill badge-primary">{{ events|length }}</span>
                                    </a>
                                    {% endif %}
                                </div>

                                {% if events %}
                                <div class="small mt-1">
                                    {% for event in events[:2] %}
                                    <div class="text-truncate" style="max-width: 100%;" title="{{ event.description }}">
                                        <i class="fas fa-user-tie text-primary"></i> {{ event.formateur.nom_prenom }}
                                    </div>
                                    {% endfor %}
                                    {% if events|length > 2 %}
                                    <div class="text-muted">+ {{ events|length - 2 }} autres...</div>
                                    {% endif %}
                                </div>

                                <!-- Modal pour afficher tous les événements du jour -->
                                <div class="modal fade" id="eventsModal{{ day }}" tabindex="-1" role="dialog" aria-labelledby="eventsModalLabel{{ day }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="eventsModalLabel{{ day }}">Rendez-vous du {{ day }}/{{ current_month }}/{{ current_year }}</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <ul class="list-group">
                                                    {% for event in events %}
                                                    <li class="list-group-item">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <strong>{{ event.formateur.nom_prenom }}</strong>
                                                                <p class="mb-0 text-muted">{{ event.description }}</p>
                                                            </div>
                                                            <div class="btn-group" role="group">
                                                                <a href="{{ url_for('edit_agenda', id=event.id) }}" class="btn btn-sm btn-warning">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="{{ url_for('delete_agenda', id=event.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce rendez-vous?');">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Fermer</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                {% endif %}
                            </td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Rendez-vous</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Formateur</th>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for agenda in agendas %}
                        <tr>
                            <td>{{ agenda.id }}</td>
                            <td>{{ agenda.formateur.nom_prenom }}</td>
                            <td>{{ agenda.date_rendezvous.strftime('%d/%m/%Y') }}</td>
                            <td>{{ agenda.description }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_agenda', id=agenda.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('print_agenda', id=agenda.id) }}" class="btn btn-sm btn-info" title="Imprimer" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteAgendaModal{{ agenda.id }}" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Modal pour confirmer la suppression -->
                                <div class="modal fade" id="deleteAgendaModal{{ agenda.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteAgendaModalLabel{{ agenda.id }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteAgendaModalLabel{{ agenda.id }}">Confirmer la suppression</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                Êtes-vous sûr de vouloir supprimer ce rendez-vous avec <strong>{{ agenda.formateur.nom_prenom }}</strong> le <strong>{{ agenda.date_rendezvous.strftime('%d/%m/%Y') }}</strong> ?
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                                                <a href="{{ url_for('delete_agenda', id=agenda.id) }}" class="btn btn-danger">Supprimer</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='agenda') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
