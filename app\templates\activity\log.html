{% extends "base.html" %}

{% block title %}سجل الأنشطة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-history"></i> سجل أنشطة المستخدمين
                    </h4>
                </div>
                <div class="card-body">
                    <!-- نموذج البحث والتصفية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="POST" class="border p-3 rounded bg-light">
                                {{ form.hidden_tag() }}
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            {{ form.nom_utilisateur.label(class="form-label") }}
                                            {{ form.nom_utilisateur(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            {{ form.type_action.label(class="form-label") }}
                                            {{ form.type_action(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            {{ form.module_concerne.label(class="form-label") }}
                                            {{ form.module_concerne(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            {{ form.date_debut.label(class="form-label") }}
                                            {{ form.date_debut(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            {{ form.date_fin.label(class="form-label") }}
                                            {{ form.date_fin(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="form-group">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn-primary d-block">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- جدول الأنشطة -->
                    {% if activities.items %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ والوقت</th>
                                        <th>المستخدم</th>
                                        <th>نوع العملية</th>
                                        <th>الوحدة</th>
                                        <th>الوصف</th>
                                        <th>عنوان IP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for activity in activities.items %}
                                    <tr>
                                        <td>
                                            <small>
                                                {{ activity.date_action.strftime('%Y-%m-%d') }}<br>
                                                {{ activity.date_action.strftime('%H:%M:%S') }}
                                            </small>
                                        </td>
                                        <td>
                                            <strong>{{ activity.nom_utilisateur }}</strong>
                                        </td>
                                        <td>
                                            {% if activity.type_action == 'create' %}
                                                <span class="badge bg-success">إنشاء</span>
                                            {% elif activity.type_action == 'update' %}
                                                <span class="badge bg-warning">تحديث</span>
                                            {% elif activity.type_action == 'delete' %}
                                                <span class="badge bg-danger">حذف</span>
                                            {% elif activity.type_action == 'view' %}
                                                <span class="badge bg-info">عرض</span>
                                            {% elif activity.type_action == 'login' %}
                                                <span class="badge bg-primary">دخول</span>
                                            {% elif activity.type_action == 'logout' %}
                                                <span class="badge bg-secondary">خروج</span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">{{ activity.type_action }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ activity.module_concerne }}</span>
                                        </td>
                                        <td>
                                            {% if activity.description_action %}
                                                {{ activity.description_action }}
                                            {% else %}
                                                <em class="text-muted">لا يوجد وصف</em>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ activity.adresse_ip }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- التنقل بين الصفحات -->
                        {% if activities.pages > 1 %}
                        <nav aria-label="تنقل الصفحات">
                            <ul class="pagination justify-content-center">
                                {% if activities.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('activity_log', page=activities.prev_num) }}">السابق</a>
                                    </li>
                                {% endif %}
                                
                                {% for page_num in activities.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != activities.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('activity_log', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">…</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if activities.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('activity_log', page=activities.next_num) }}">التالي</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                        <div class="mt-3">
                            <small class="text-muted">
                                عرض {{ activities.items|length }} من أصل {{ activities.total }} نشاط
                            </small>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            لا توجد أنشطة مسجلة حالياً
                        </div>
                    {% endif %}

                    <div class="mt-4">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
