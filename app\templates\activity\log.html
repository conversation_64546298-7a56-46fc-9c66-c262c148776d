{% extends "base.html" %}

{% block title %}Journal d'Activité{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-history me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Journal d'Activité</span>
                        </h4>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Formulaire de recherche et filtrage -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="POST" class="border p-3 rounded bg-light">
                                {{ form.hidden_tag() }}
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            {{ form.nom_utilisateur.label(class="form-label") }}
                                            {{ form.nom_utilisateur(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            {{ form.type_action.label(class="form-label") }}
                                            {{ form.type_action(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            {{ form.module_concerne.label(class="form-label") }}
                                            {{ form.module_concerne(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            {{ form.date_debut.label(class="form-label") }}
                                            {{ form.date_debut(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            {{ form.date_fin.label(class="form-label") }}
                                            {{ form.date_fin(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="form-group">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn-primary d-block">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Tableau des activités -->
                    {% if activities.items %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date et Heure</th>
                                        <th>Utilisateur</th>
                                        <th>Type d'Opération</th>
                                        <th>Module</th>
                                        <th>Description</th>
                                        <th>Adresse IP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for activity in activities.items %}
                                    <tr>
                                        <td>
                                            <small>
                                                {{ activity.date_action.strftime('%Y-%m-%d') }}<br>
                                                {{ activity.date_action.strftime('%H:%M:%S') }}
                                            </small>
                                        </td>
                                        <td>
                                            <strong>{{ activity.nom_utilisateur }}</strong>
                                        </td>
                                        <td>
                                            {% if activity.type_action == 'create' %}
                                                <span class="badge bg-success">Création</span>
                                            {% elif activity.type_action == 'update' %}
                                                <span class="badge bg-warning">Modification</span>
                                            {% elif activity.type_action == 'delete' %}
                                                <span class="badge bg-danger">Suppression</span>
                                            {% elif activity.type_action == 'view' %}
                                                <span class="badge bg-info">Consultation</span>
                                            {% elif activity.type_action == 'login' %}
                                                <span class="badge bg-primary">Connexion</span>
                                            {% elif activity.type_action == 'logout' %}
                                                <span class="badge bg-secondary">Déconnexion</span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">{{ activity.type_action }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ activity.module_concerne }}</span>
                                        </td>
                                        <td>
                                            {% if activity.description_action %}
                                                {{ activity.description_action }}
                                            {% else %}
                                                <em class="text-muted">Aucune description</em>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ activity.adresse_ip }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Navigation entre les pages -->
                        {% if activities.pages > 1 %}
                        <nav aria-label="Navigation des pages">
                            <ul class="pagination justify-content-center">
                                {% if activities.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('activity_log', page=activities.prev_num) }}">Précédent</a>
                                    </li>
                                {% endif %}
                                
                                {% for page_num in activities.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != activities.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('activity_log', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">…</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if activities.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('activity_log', page=activities.next_num) }}">Suivant</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                        <div class="mt-3">
                            <small class="text-muted">
                                Affichage de {{ activities.items|length }} sur {{ activities.total }} activités
                            </small>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            Aucune activité enregistrée actuellement
                        </div>
                    {% endif %}

                    <div class="mt-4">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour au Tableau de Bord
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
