#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تحديث قاعدة البيانات لإضافة النماذج الجديدة
"""

from app import create_app, db
from app.models import (
    CompanyInfo, UserActivityLog, BackupSettings, BackupLog
)
from datetime import datetime, timezone

app = create_app()

def update_database():
    """تحديث قاعدة البيانات بالنماذج الجديدة"""
    with app.app_context():
        try:
            # إنشاء الجداول الجديدة
            db.create_all()
            
            # إضافة إعدادات افتراضية لمعلومات الشركة
            if not CompanyInfo.query.first():
                company_info = CompanyInfo(
                    nom_entreprise='مركز التكوين المهني',
                    slogan='التميز في التكوين والتطوير',
                    adresse='الدار البيضاء، المغرب',
                    telephone='+*********** 000',
                    email='<EMAIL>',
                    pied_de_page='جميع الحقوق محفوظة © 2025 مركز التكوين المهني',
                    couleur_principale='#007bff',
                    couleur_secondaire='#6c757d'
                )
                db.session.add(company_info)
            
            # إضافة إعدادات افتراضية للنسخ الاحتياطي
            if not BackupSettings.query.first():
                backup_settings = BackupSettings(
                    backup_automatique=False,
                    frequence_backup='daily',
                    heure_backup=datetime.strptime('02:00', '%H:%M').time(),
                    nombre_max_backups=10,
                    compression=True,
                    notification_email=False
                )
                db.session.add(backup_settings)
            
            db.session.commit()
            print("✅ تم تحديث قاعدة البيانات بنجاح!")
            print("✅ تم إضافة النماذج الجديدة:")
            print("   - نموذج معلومات الشركة (CompanyInfo)")
            print("   - نموذج سجل أنشطة المستخدمين (UserActivityLog)")
            print("   - نموذج إعدادات النسخ الاحتياطي (BackupSettings)")
            print("   - نموذج سجل النسخ الاحتياطية (BackupLog)")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
            db.session.rollback()

if __name__ == '__main__':
    update_database()
