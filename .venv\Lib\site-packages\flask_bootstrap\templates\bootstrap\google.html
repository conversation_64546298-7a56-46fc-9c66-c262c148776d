{% macro analytics(account) -%}
<script type="text/javascript">
  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', '{{account}}']);
  _gaq.push(['_trackPageview']);

  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();
</script>
{% endmacro %}

{% macro uanalytics(id, options='auto', domain=None) %}
{# The uanalytics macro currently contains a hack to support legacy code.
   The old signature was ``uanalytics(id, domain)`` when domain was a required
   parameter that was passed on to the ga() function.

   To preserve old behavior, if options is not a dictionary, it is passed on
   unchanged. The ``domain`` parameter is added to not break calls with named
   parameters, it will override any other value for options.

   More modern code can simply pass any desired option to the analytics
   function as desired.
#}
{%- if domain != None %}
{%- set options = domain %}
{%- endif %}
<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');
  ga('create', {{id|tojson|safe}}, {{options|tojson|safe}});
  ga('send', 'pageview');
</script>
{% endmacro %}
