{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-graduation-cap me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Formations</span>
                        </h4>
                        <a href="{{ url_for('new_formation') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nouvelle Formation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Thème</th>
                                <th>Organisme</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if formations %}
                                {% for formation in formations %}
                                    <tr>
                                        <td>{{ formation.id }}</td>
                                        <td>{{ formation.theme }}</td>
                                        <td>{{ formation.organisme.raison_sociale if formation.organisme else '-' }}</td>
                                        <td>{{ formation.date.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('edit_formation', id=formation.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                                        data-delete-url="{{ url_for('delete_formation', id=formation.id) }}"
                                                        data-delete-message="Êtes-vous sûr de vouloir supprimer cette formation?"
                                                        data-use-modal="true"
                                                        data-delete-title="Confirmer la suppression">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">Aucune formation trouvée</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='formations') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
