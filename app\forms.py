from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField, DateField, SelectField, TextAreaField, IntegerField, FloatField, TimeField
from wtforms.validators import DataRequired, Email, ValidationError, Length, Optional, NumberRange
from app.models import User, Organisme, Formateur, Domaine, Theme

class LoginForm(FlaskForm):
    username = <PERSON><PERSON>ield('Nom d\'utilisateur', validators=[DataRequired()])
    password = PasswordField('Mot de passe', validators=[DataRequired()])
    remember_me = BooleanField('Se souvenir de moi')
    submit = SubmitField('Se connecter')

class RegistrationForm(FlaskForm):
    username = StringField('Nom d\'utilisateur', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = Password<PERSON>ield('Mot de passe', validators=[DataRequired()])
    submit = SubmitField('S\'inscrire')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Ce nom d\'utilisateur est déjà pris.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('Cette adresse email est déjà utilisée.')

class FormationForm(FlaskForm):
    theme = StringField('Thème', validators=[DataRequired()])
    date = DateField('Date', validators=[DataRequired()])
    organisme = SelectField('Organisme', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Enregistrer')

class UserForm(FlaskForm):
    username = StringField('Nom d\'utilisateur', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    nom_complet = StringField('Nom complet', validators=[DataRequired(), Length(max=100)])
    password = PasswordField('Mot de passe', validators=[DataRequired(), Length(min=6)])
    is_admin = BooleanField('Administrateur')

    # Permissions
    perm_fiche_inscription = BooleanField('Fiche d\'inscription')
    perm_dossier_technique = BooleanField('Dossier technique')
    perm_dossier_remboursement = BooleanField('Dossier de remboursement')
    perm_organisme = BooleanField('Organisme')
    perm_formateur = BooleanField('Formateur')
    perm_agenda = BooleanField('Agenda des formateurs')
    perm_domaine_theme = BooleanField('Domaines et Thèmes')

    submit = SubmitField('Enregistrer')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None and user.id != getattr(self, 'user_id', None):
            raise ValidationError('Ce nom d\'utilisateur est déjà pris.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None and user.id != getattr(self, 'user_id', None):
            raise ValidationError('Cette adresse email est déjà utilisée.')

class OrganismeForm(FlaskForm):
    raison_sociale = StringField('Raison Sociale', validators=[DataRequired(), Length(max=255)])
    forme_juridique = StringField('Forme juridique', validators=[DataRequired(), Length(max=100)])
    date_creation = DateField('Date de création', validators=[DataRequired()])
    nom_prenom_gerant = StringField('Nom et prénom du gérant', validators=[DataRequired(), Length(max=255)])
    adresse = StringField('Adresse', validators=[DataRequired(), Length(max=255)])
    ville = StringField('Ville', validators=[DataRequired(), Length(max=100)])
    telephone = StringField('Téléphone', validators=[DataRequired(), Length(max=50)])
    fax = StringField('Fax', validators=[Optional(), Length(max=50)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    patente = StringField('Patente', validators=[Optional(), Length(max=100)])
    identifiant_fiscal = StringField('Identifiant fiscal', validators=[Optional(), Length(max=100)])
    num_rc = StringField('N° RC', validators=[Optional(), Length(max=100)])
    num_cnss = StringField('N° CNSS', validators=[Optional(), Length(max=100)])
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class FormateurForm(FlaskForm):
    nom_prenom = StringField('Nom et prénom du formateur', validators=[DataRequired(), Length(max=255)])
    specialite = StringField('Spécialité', validators=[DataRequired(), Length(max=255)])
    cv = BooleanField('C.V')
    diplomes = BooleanField('Diplômes')
    adresse = StringField('Adresse', validators=[DataRequired(), Length(max=255)])
    ville = StringField('Ville', validators=[DataRequired(), Length(max=100)])
    num_tel = StringField('N° de téléphone', validators=[DataRequired(), Length(max=50)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class AgendaFormateurForm(FlaskForm):
    formateur = SelectField('Formateur', coerce=int, validators=[DataRequired()])
    date_rendezvous = DateField('Date du rendez-vous', validators=[DataRequired()])
    description = TextAreaField('Description', validators=[DataRequired(), Length(max=500)])
    submit = SubmitField('Enregistrer')

class DomaineForm(FlaskForm):
    nom = StringField('Nom du domaine', validators=[DataRequired(), Length(max=255)])
    submit = SubmitField('Enregistrer')

class ThemeForm(FlaskForm):
    nom = StringField('Nom du thème', validators=[DataRequired(), Length(max=255)])
    domaine = SelectField('Domaine', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Enregistrer')

class FicheInscriptionForm(FlaskForm):
    date_inscription = DateField('Date de l\'inscription', validators=[Optional()])
    raison_sociale = StringField('Raison Sociale', validators=[DataRequired(), Length(max=255)])
    tel_entreprise = StringField('Téléphone de l\'entreprise', validators=[DataRequired(), Length(max=50)])
    fax_entreprise = StringField('Fax de l\'entreprise', validators=[Optional(), Length(max=50)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    mot_de_passe_email = StringField('Mot de passe email', validators=[Optional(), Length(max=128)])
    patente = StringField('Patente', validators=[Optional(), Length(max=100)])
    identifiant_fiscale = StringField('Identifiant fiscale', validators=[Optional(), Length(max=100)])
    num_rc = StringField('N° RC', validators=[Optional(), Length(max=100)])
    num_cnss = StringField('N° CNSS', validators=[Optional(), Length(max=100)])
    eligible = BooleanField('Éligible')
    ice = StringField('ICE', validators=[Optional(), Length(max=100)])
    mot_de_passe_ice = StringField('Mot de passe ICE', validators=[Optional(), Length(max=128)])
    nombre_cadres = IntegerField('Nombre des cadres', validators=[Optional(), NumberRange(min=0)])
    nombre_employes = IntegerField('Nombre des employés', validators=[Optional(), NumberRange(min=0)])
    nombre_ouvriers = IntegerField('Nombre des ouvriers', validators=[Optional(), NumberRange(min=0)])
    validation = BooleanField('Validation')
    date_validation = DateField('Date de validation', validators=[Optional()])
    depot_physique = BooleanField('Dépôt physique')
    date_depot = DateField('Date de dépôt', validators=[Optional()])
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class DossierTechniqueForm(FlaskForm):
    fiche_inscription = SelectField('Entreprise', coerce=int, validators=[DataRequired()])
    domaine = SelectField('Domaine', coerce=int, validators=[DataRequired()])
    theme = SelectField('Thème', coerce=int, validators=[DataRequired()])
    objectif = TextAreaField('Objectif (compétence visée)', validators=[DataRequired()])
    contenu_indicatif = TextAreaField('Contenu indicatif', validators=[DataRequired()])
    organisme_formation = SelectField('Organisme de Formation', coerce=int, validators=[DataRequired()])
    num_cnss_organisme = StringField('N° CNSS de l\'organisme', validators=[Optional(), Length(max=100)])
    type_formation = StringField('Type de formation', validators=[DataRequired(), Length(max=100)])
    cout_formation_ht = FloatField('Coût de la Formation HT', validators=[DataRequired(), NumberRange(min=0)])
    effectif_global = IntegerField('Effectif global', validators=[DataRequired(), NumberRange(min=1)])
    nombre_cadres = IntegerField('Nombre des cadres', validators=[Optional(), NumberRange(min=0)])
    nombre_employes = IntegerField('Nombre des employés', validators=[Optional(), NumberRange(min=0)])
    nombre_ouvriers = IntegerField('Nombre des ouvriers', validators=[Optional(), NumberRange(min=0)])
    conforme = BooleanField('Conforme')
    depot_physique = BooleanField('Dépôt physique')
    date_depot = DateField('Date de dépôt', validators=[Optional()])
    validation = BooleanField('Validation')
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class DossierRemboursementForm(FlaskForm):
    organisme = SelectField('Organisme', coerce=int, validators=[DataRequired()])
    formateur = SelectField('Formateur', coerce=int, validators=[DataRequired()])
    fiche_inscription = SelectField('Entreprise', coerce=int, validators=[DataRequired()])
    theme = StringField('Thème', validators=[DataRequired(), Length(max=255)])
    date = DateField('Date', validators=[DataRequired()])
    contrat = StringField('Contrat', validators=[DataRequired(), Length(max=255)])
    f2 = BooleanField('F2')
    liste_de_presence = BooleanField('Liste de présence')
    fiche_synthetique_evaluation_formateur = BooleanField('Fiche synthétique d\'évaluation de formateur')
    f4 = BooleanField('F4')
    facturation = SelectField('Facturation', choices=[('par action', 'Par action'), ('globale', 'Globale')], validators=[DataRequired()])
    mode_de_reglement = SelectField('Mode de règlement', choices=[('par cheque', 'Par chèque'), ('par virement', 'Par virement')], validators=[DataRequired()])
    m6 = BooleanField('M6')
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class RapportForm(FlaskForm):
    titre = StringField('Titre', validators=[DataRequired(), Length(max=255)])
    type_rapport = SelectField('Type de rapport', choices=[
        ('fiche_inscription', 'Fiche d\'inscription'),
        ('dossier_technique', 'Dossier technique'),
        ('dossier_remboursement', 'Dossier de remboursement'),
        ('organisme', 'Organisme'),
        ('formateur', 'Formateur'),
        ('agenda', 'Agenda des formateurs'),
        ('domaine_theme', 'Domaines et Thèmes')
    ], validators=[DataRequired()])
    contenu = TextAreaField('Contenu', validators=[Optional()])
    submit = SubmitField('Générer')


# نموذج معلومات الشركة
class CompanyInfoForm(FlaskForm):
    nom_entreprise = StringField('اسم الشركة', validators=[DataRequired(), Length(max=255)])
    slogan = StringField('شعار الشركة', validators=[Optional(), Length(max=500)])
    adresse = TextAreaField('العنوان', validators=[Optional()])
    telephone = StringField('الهاتف', validators=[Optional(), Length(max=50)])
    fax = StringField('الفاكس', validators=[Optional(), Length(max=50)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email(), Length(max=120)])
    site_web = StringField('الموقع الإلكتروني', validators=[Optional(), Length(max=255)])
    logo = FileField('شعار الشركة', validators=[FileAllowed(['jpg', 'png', 'gif', 'jpeg'], 'الصور فقط!')])
    pied_de_page = TextAreaField('نص أسفل الصفحة', validators=[Optional()])
    couleur_principale = StringField('اللون الأساسي', validators=[Optional(), Length(max=7)], default='#007bff')
    couleur_secondaire = StringField('اللون الثانوي', validators=[Optional(), Length(max=7)], default='#6c757d')
    submit = SubmitField('حفظ')


# نموذج إعدادات النسخ الاحتياطي
class BackupSettingsForm(FlaskForm):
    backup_automatique = BooleanField('تفعيل النسخ الاحتياطي التلقائي')
    frequence_backup = SelectField('تكرار النسخ الاحتياطي', choices=[
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري')
    ], validators=[DataRequired()])
    heure_backup = TimeField('وقت النسخ الاحتياطي', validators=[DataRequired()])
    dossier_backup = StringField('مجلد النسخ الاحتياطية', validators=[Optional(), Length(max=500)])
    nombre_max_backups = IntegerField('عدد النسخ المحفوظة', validators=[DataRequired(), NumberRange(min=1, max=100)], default=10)
    compression = BooleanField('ضغط النسخ الاحتياطية', default=True)
    notification_email = BooleanField('إرسال إشعار بالبريد الإلكتروني')
    email_notification = StringField('البريد الإلكتروني للإشعارات', validators=[Optional(), Email(), Length(max=120)])
    submit = SubmitField('حفظ الإعدادات')


# نموذج النسخ الاحتياطي اليدوي
class ManualBackupForm(FlaskForm):
    nom_fichier = StringField('اسم النسخة الاحتياطية', validators=[Optional(), Length(max=255)])
    compression = BooleanField('ضغط النسخة الاحتياطية', default=True)
    submit = SubmitField('إنشاء نسخة احتياطية')


# نموذج استيراد قاعدة البيانات
class ImportDatabaseForm(FlaskForm):
    fichier_backup = FileField('ملف النسخة الاحتياطية', validators=[
        DataRequired(),
        FileAllowed(['sql', 'db', 'sqlite', 'zip'], 'ملفات قاعدة البيانات فقط!')
    ])
    remplacer_donnees = BooleanField('استبدال البيانات الحالية', default=False)
    submit = SubmitField('استيراد')


# نموذج البحث في سجل الأنشطة
class ActivityLogSearchForm(FlaskForm):
    nom_utilisateur = StringField('اسم المستخدم', validators=[Optional(), Length(max=100)])
    type_action = SelectField('نوع العمل', choices=[
        ('', 'جميع الأنواع'),
        ('create', 'إضافة'),
        ('update', 'تعديل'),
        ('delete', 'حذف'),
        ('view', 'عرض'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج')
    ], validators=[Optional()])
    module_concerne = SelectField('الوحدة', choices=[
        ('', 'جميع الوحدات'),
        ('fiche_inscription', 'فيش التسجيل'),
        ('dossier_technique', 'الملف الفني'),
        ('dossier_remboursement', 'ملف الاسترداد'),
        ('organisme', 'المنظمات'),
        ('formateur', 'المدربين'),
        ('agenda', 'الأجندة'),
        ('domaine_theme', 'المجالات والمواضيع'),
        ('users', 'المستخدمين'),
        ('company_info', 'معلومات الشركة'),
        ('backup', 'النسخ الاحتياطي')
    ], validators=[Optional()])
    date_debut = DateField('من تاريخ', validators=[Optional()])
    date_fin = DateField('إلى تاريخ', validators=[Optional()])
    submit = SubmitField('بحث')
