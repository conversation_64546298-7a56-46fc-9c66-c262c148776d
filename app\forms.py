from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField, DateField, SelectField, TextAreaField, IntegerField, FloatField, TimeField
from wtforms.validators import DataRequired, Email, ValidationError, Length, Optional, NumberRange
from app.models import User, Organisme, Formateur, Domaine, Theme

class LoginForm(FlaskForm):
    username = <PERSON><PERSON>ield('Nom d\'utilisateur', validators=[DataRequired()])
    password = PasswordField('Mot de passe', validators=[DataRequired()])
    remember_me = BooleanField('Se souvenir de moi')
    submit = SubmitField('Se connecter')

class RegistrationForm(FlaskForm):
    username = StringField('Nom d\'utilisateur', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = Password<PERSON>ield('Mot de passe', validators=[DataRequired()])
    submit = SubmitField('S\'inscrire')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Ce nom d\'utilisateur est déjà pris.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('Cette adresse email est déjà utilisée.')

class FormationForm(FlaskForm):
    theme = StringField('Thème', validators=[DataRequired()])
    date = DateField('Date', validators=[DataRequired()])
    organisme = SelectField('Organisme', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Enregistrer')

class UserForm(FlaskForm):
    username = StringField('Nom d\'utilisateur', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    nom_complet = StringField('Nom complet', validators=[DataRequired(), Length(max=100)])
    password = PasswordField('Mot de passe', validators=[DataRequired(), Length(min=6)])
    is_admin = BooleanField('Administrateur')

    # Permissions
    perm_fiche_inscription = BooleanField('Fiche d\'inscription')
    perm_dossier_technique = BooleanField('Dossier technique')
    perm_dossier_remboursement = BooleanField('Dossier de remboursement')
    perm_organisme = BooleanField('Organisme')
    perm_formateur = BooleanField('Formateur')
    perm_agenda = BooleanField('Agenda des formateurs')
    perm_domaine_theme = BooleanField('Domaines et Thèmes')
    perm_users = BooleanField('Gestion des utilisateurs')
    perm_company_info = BooleanField('Informations de l\'entreprise')
    perm_activity_log = BooleanField('Journal d\'activité')
    perm_backup = BooleanField('Sauvegarde et restauration')
    perm_reports = BooleanField('Rapports et impressions')

    submit = SubmitField('Enregistrer')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None and user.id != getattr(self, 'user_id', None):
            raise ValidationError('Ce nom d\'utilisateur est déjà pris.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None and user.id != getattr(self, 'user_id', None):
            raise ValidationError('Cette adresse email est déjà utilisée.')

class UserEditForm(FlaskForm):
    username = StringField('Nom d\'utilisateur', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    nom_complet = StringField('Nom complet', validators=[DataRequired(), Length(max=100)])
    password = PasswordField('Nouveau mot de passe (laisser vide pour conserver l\'actuel)', validators=[Optional(), Length(min=6)])
    is_admin = BooleanField('Administrateur')

    # Permissions
    perm_fiche_inscription = BooleanField('Fiche d\'inscription')
    perm_dossier_technique = BooleanField('Dossier technique')
    perm_dossier_remboursement = BooleanField('Dossier de remboursement')
    perm_organisme = BooleanField('Organisme')
    perm_formateur = BooleanField('Formateur')
    perm_agenda = BooleanField('Agenda des formateurs')
    perm_domaine_theme = BooleanField('Domaines et Thèmes')
    perm_users = BooleanField('Gestion des utilisateurs')
    perm_company_info = BooleanField('Informations de l\'entreprise')
    perm_activity_log = BooleanField('Journal d\'activité')
    perm_backup = BooleanField('Sauvegarde et restauration')
    perm_reports = BooleanField('Rapports et impressions')

    submit = SubmitField('Enregistrer')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None and user.id != getattr(self, 'user_id', None):
            raise ValidationError('Ce nom d\'utilisateur est déjà pris.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None and user.id != getattr(self, 'user_id', None):
            raise ValidationError('Cette adresse email est déjà utilisée.')

class OrganismeForm(FlaskForm):
    raison_sociale = StringField('Raison Sociale', validators=[DataRequired(), Length(max=255)])
    forme_juridique = StringField('Forme juridique', validators=[DataRequired(), Length(max=100)])
    date_creation = DateField('Date de création', validators=[DataRequired()])
    nom_prenom_gerant = StringField('Nom et prénom du gérant', validators=[DataRequired(), Length(max=255)])
    adresse = StringField('Adresse', validators=[DataRequired(), Length(max=255)])
    ville = StringField('Ville', validators=[DataRequired(), Length(max=100)])
    telephone = StringField('Téléphone', validators=[DataRequired(), Length(max=50)])
    fax = StringField('Fax', validators=[Optional(), Length(max=50)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    patente = StringField('Patente', validators=[Optional(), Length(max=100)])
    identifiant_fiscal = StringField('Identifiant fiscal', validators=[Optional(), Length(max=100)])
    num_rc = StringField('N° RC', validators=[Optional(), Length(max=100)])
    num_cnss = StringField('N° CNSS', validators=[Optional(), Length(max=100)])
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class FormateurForm(FlaskForm):
    nom_prenom = StringField('Nom et prénom du formateur', validators=[DataRequired(), Length(max=255)])
    specialite = StringField('Spécialité', validators=[DataRequired(), Length(max=255)])
    cv = BooleanField('C.V')
    diplomes = BooleanField('Diplômes')
    adresse = StringField('Adresse', validators=[DataRequired(), Length(max=255)])
    ville = StringField('Ville', validators=[DataRequired(), Length(max=100)])
    num_tel = StringField('N° de téléphone', validators=[DataRequired(), Length(max=50)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class AgendaFormateurForm(FlaskForm):
    formateur = SelectField('Formateur', coerce=int, validators=[DataRequired()])
    date_rendezvous = DateField('Date du rendez-vous', validators=[DataRequired()])
    description = TextAreaField('Description', validators=[DataRequired(), Length(max=500)])
    submit = SubmitField('Enregistrer')

class DomaineForm(FlaskForm):
    nom = StringField('Nom du domaine', validators=[DataRequired(), Length(max=255)])
    submit = SubmitField('Enregistrer')

class ThemeForm(FlaskForm):
    nom = StringField('Nom du thème', validators=[DataRequired(), Length(max=255)])
    domaine = SelectField('Domaine', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Enregistrer')

class FicheInscriptionForm(FlaskForm):
    date_inscription = DateField('Date de l\'inscription', validators=[Optional()])
    raison_sociale = StringField('Raison Sociale', validators=[DataRequired(), Length(max=255)])
    tel_entreprise = StringField('Téléphone de l\'entreprise', validators=[DataRequired(), Length(max=50)])
    fax_entreprise = StringField('Fax de l\'entreprise', validators=[Optional(), Length(max=50)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    mot_de_passe_email = StringField('Mot de passe email', validators=[Optional(), Length(max=128)])
    patente = StringField('Patente', validators=[Optional(), Length(max=100)])
    identifiant_fiscale = StringField('Identifiant fiscale', validators=[Optional(), Length(max=100)])
    num_rc = StringField('N° RC', validators=[Optional(), Length(max=100)])
    num_cnss = StringField('N° CNSS', validators=[Optional(), Length(max=100)])
    eligible = BooleanField('Éligible')
    ice = StringField('ICE', validators=[Optional(), Length(max=100)])
    mot_de_passe_ice = StringField('Mot de passe ICE', validators=[Optional(), Length(max=128)])
    nombre_cadres = IntegerField('Nombre des cadres', validators=[Optional(), NumberRange(min=0)])
    nombre_employes = IntegerField('Nombre des employés', validators=[Optional(), NumberRange(min=0)])
    nombre_ouvriers = IntegerField('Nombre des ouvriers', validators=[Optional(), NumberRange(min=0)])
    validation = BooleanField('Validation')
    date_validation = DateField('Date de validation', validators=[Optional()])
    depot_physique = BooleanField('Dépôt physique')
    date_depot = DateField('Date de dépôt', validators=[Optional()])
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class DossierTechniqueForm(FlaskForm):
    fiche_inscription = SelectField('Entreprise', coerce=int, validators=[DataRequired()])
    domaine = SelectField('Domaine', coerce=int, validators=[DataRequired()])
    theme = SelectField('Thème', coerce=int, validators=[DataRequired()])
    objectif = TextAreaField('Objectif (compétence visée)', validators=[DataRequired()])
    contenu_indicatif = TextAreaField('Contenu indicatif', validators=[DataRequired()])
    organisme_formation = SelectField('Organisme de Formation', coerce=int, validators=[DataRequired()])
    num_cnss_organisme = StringField('N° CNSS de l\'organisme', validators=[Optional(), Length(max=100)])
    type_formation = StringField('Type de formation', validators=[DataRequired(), Length(max=100)])
    cout_formation_ht = FloatField('Coût de la Formation HT', validators=[DataRequired(), NumberRange(min=0)])
    effectif_global = IntegerField('Effectif global', validators=[DataRequired(), NumberRange(min=1)])
    nombre_cadres = IntegerField('Nombre des cadres', validators=[Optional(), NumberRange(min=0)])
    nombre_employes = IntegerField('Nombre des employés', validators=[Optional(), NumberRange(min=0)])
    nombre_ouvriers = IntegerField('Nombre des ouvriers', validators=[Optional(), NumberRange(min=0)])
    conforme = BooleanField('Conforme')
    depot_physique = BooleanField('Dépôt physique')
    date_depot = DateField('Date de dépôt', validators=[Optional()])
    validation = BooleanField('Validation')
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class DossierRemboursementForm(FlaskForm):
    organisme = SelectField('Organisme', coerce=int, validators=[DataRequired()])
    formateur = SelectField('Formateur', coerce=int, validators=[DataRequired()])
    fiche_inscription = SelectField('Entreprise', coerce=int, validators=[DataRequired()])
    theme = StringField('Thème', validators=[DataRequired(), Length(max=255)])
    date = DateField('Date', validators=[DataRequired()])
    contrat = StringField('Contrat', validators=[DataRequired(), Length(max=255)])
    f2 = BooleanField('F2')
    liste_de_presence = BooleanField('Liste de présence')
    fiche_synthetique_evaluation_formateur = BooleanField('Fiche synthétique d\'évaluation de formateur')
    f4 = BooleanField('F4')
    facturation = SelectField('Facturation', choices=[('par action', 'Par action'), ('globale', 'Globale')], validators=[DataRequired()])
    mode_de_reglement = SelectField('Mode de règlement', choices=[('par cheque', 'Par chèque'), ('par virement', 'Par virement')], validators=[DataRequired()])
    m6 = BooleanField('M6')
    piece_jointe = FileField('Pièce jointe', validators=[Optional(), FileAllowed(['pdf', 'jpg', 'png'], 'PDF, JPG ou PNG uniquement!')])
    submit = SubmitField('Enregistrer')

class RapportForm(FlaskForm):
    titre = StringField('Titre', validators=[DataRequired(), Length(max=255)])
    type_rapport = SelectField('Type de rapport', choices=[
        ('fiche_inscription', 'Fiche d\'inscription'),
        ('dossier_technique', 'Dossier technique'),
        ('dossier_remboursement', 'Dossier de remboursement'),
        ('organisme', 'Organisme'),
        ('formateur', 'Formateur'),
        ('agenda', 'Agenda des formateurs'),
        ('domaine_theme', 'Domaines et Thèmes')
    ], validators=[DataRequired()])
    contenu = TextAreaField('Contenu', validators=[Optional()])
    submit = SubmitField('Générer')


# Formulaire informations société
class CompanyInfoForm(FlaskForm):
    nom_entreprise = StringField('Nom de la Société', validators=[DataRequired(), Length(max=255)])
    slogan = StringField('Slogan de la Société', validators=[Optional(), Length(max=500)])
    adresse = TextAreaField('Adresse', validators=[Optional()])
    telephone = StringField('Téléphone', validators=[Optional(), Length(max=50)])
    fax = StringField('Fax', validators=[Optional(), Length(max=50)])
    email = StringField('Email', validators=[Optional(), Email(), Length(max=120)])
    site_web = StringField('Site Web', validators=[Optional(), Length(max=255)])
    logo = FileField('Logo de la Société', validators=[FileAllowed(['jpg', 'png', 'gif', 'jpeg'], 'Images seulement!')])
    pied_de_page = TextAreaField('Pied de Page', validators=[Optional()])
    couleur_principale = StringField('Couleur Primaire', validators=[Optional(), Length(max=7)], default='#007bff')
    couleur_secondaire = StringField('Couleur Secondaire', validators=[Optional(), Length(max=7)], default='#6c757d')
    submit = SubmitField('Enregistrer')


# Formulaire paramètres de sauvegarde
class BackupSettingsForm(FlaskForm):
    backup_automatique = BooleanField('Activer la Sauvegarde Automatique')
    frequence_backup = SelectField('Fréquence de Sauvegarde', choices=[
        ('daily', 'Quotidienne'),
        ('weekly', 'Hebdomadaire'),
        ('monthly', 'Mensuelle')
    ], validators=[DataRequired()])
    heure_backup = TimeField('Heure de Sauvegarde', validators=[DataRequired()])
    dossier_backup = StringField('Dossier de Sauvegarde', validators=[Optional(), Length(max=500)])
    nombre_max_backups = IntegerField('Nombre de Sauvegardes à Conserver', validators=[DataRequired(), NumberRange(min=1, max=100)], default=10)
    compression = BooleanField('Compresser les Sauvegardes', default=True)
    notification_email = BooleanField('Envoyer Notification par Email')
    email_notification = StringField('Email pour Notifications', validators=[Optional(), Email(), Length(max=120)])
    submit = SubmitField('Enregistrer les Paramètres')


# Formulaire sauvegarde manuelle
class ManualBackupForm(FlaskForm):
    nom_fichier = StringField('Nom de la Sauvegarde', validators=[Optional(), Length(max=255)])
    compression = BooleanField('Compresser la Sauvegarde', default=True)
    submit = SubmitField('Créer Sauvegarde')


# Formulaire import base de données
class ImportDatabaseForm(FlaskForm):
    fichier_sauvegarde = FileField('Fichier de Sauvegarde', validators=[
        DataRequired(),
        FileAllowed(['sql', 'db', 'sqlite', 'zip'], 'Fichiers de base de données seulement!')
    ])
    creer_sauvegarde_avant = BooleanField('Créer une sauvegarde avant import', default=True)
    confirmer_remplacement = BooleanField('Je confirme le remplacement des données actuelles', default=False)
    submit = SubmitField('Importer')


# Formulaire recherche journal d'activité
class ActivityLogSearchForm(FlaskForm):
    nom_utilisateur = StringField('Nom Utilisateur', validators=[Optional(), Length(max=100)])
    type_action = SelectField('Type d\'Action', choices=[
        ('', 'Tous les Types'),
        ('create', 'Création'),
        ('update', 'Modification'),
        ('delete', 'Suppression'),
        ('view', 'Consultation'),
        ('login', 'Connexion'),
        ('logout', 'Déconnexion')
    ], validators=[Optional()])
    module_concerne = SelectField('Module', choices=[
        ('', 'Tous les Modules'),
        ('fiche_inscription', 'Fiche d\'Inscription'),
        ('dossier_technique', 'Dossier Technique'),
        ('dossier_remboursement', 'Dossier Remboursement'),
        ('organisme', 'Organismes'),
        ('formateur', 'Formateurs'),
        ('agenda', 'Agenda'),
        ('domaine_theme', 'Domaines et Thèmes'),
        ('users', 'Utilisateurs'),
        ('company_info', 'Informations Société'),
        ('backup', 'Sauvegarde')
    ], validators=[Optional()])
    date_debut = DateField('Date Début', validators=[Optional()])
    date_fin = DateField('Date Fin', validators=[Optional()])
    submit = SubmitField('Rechercher')
