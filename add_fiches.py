from app import create_app, db
from app.models import FicheInscription
from datetime import datetime, timezone

def add_fiches():
    app = create_app()
    with app.app_context():
        # إضافة بيانات التسجيل
        if FicheInscription.query.count() == 0:
            fiche1 = FicheInscription(
                raison_sociale='Société ABC',
                tel_entreprise='0522123456',
                fax_entreprise='0522123457',
                email='<EMAIL>',
                mot_de_passe_email='password123',
                patente='12345',
                identifiant_fiscale='IF12345',
                num_rc='RC12345',
                num_cnss='CNSS12345',
                eligible=True,
                ice='ICE12345',
                mot_de_passe_ice='password123',
                nombre_cadres=5,
                nombre_employes=20,
                nombre_ouvriers=10,
                validation=True,
                date_validation=datetime.now(timezone.utc),
                depot_physique=True,
                date_depot=datetime.now(timezone.utc)
            )
            
            fiche2 = FicheInscription(
                raison_sociale='Entreprise XYZ',
                tel_entreprise='0537123456',
                fax_entreprise='0537123457',
                email='<EMAIL>',
                mot_de_passe_email='password123',
                patente='67890',
                identifiant_fiscale='IF67890',
                num_rc='RC67890',
                num_cnss='CNSS67890',
                eligible=True,
                ice='ICE67890',
                mot_de_passe_ice='password123',
                nombre_cadres=3,
                nombre_employes=15,
                nombre_ouvriers=5,
                validation=True,
                date_validation=datetime.now(timezone.utc),
                depot_physique=True,
                date_depot=datetime.now(timezone.utc)
            )
            
            db.session.add_all([fiche1, fiche2])
            db.session.commit()
            print("Added sample fiches d'inscription")
        
        print("Sample fiches added successfully!")

if __name__ == '__main__':
    add_fiches()
