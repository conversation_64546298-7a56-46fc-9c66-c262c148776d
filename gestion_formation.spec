# -*- mode: python ; coding: utf-8 -*-
import os

a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('app/templates', 'app/templates') if os.path.exists('app/templates') else ('', '.'),
        ('app/static', 'app/static') if os.path.exists('app/static') else ('', '.'),
        ('app/static/uploads', 'app/static/uploads') if os.path.exists('app/static/uploads') else ('', '.'),
        ('app.db', '.') if os.path.exists('app.db') else ('', '.'),
    ],
    hiddenimports=['email_validator', 'flask', 'flask_sqlalchemy', 'flask_migrate', 'flask_login', 'flask_wtf', 'wtforms', 'werkzeug', 'jinja2', 'sqlalchemy', 'email_validator', 'itsdangerous', 'click', 'markupsafe'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Gestion_Formation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
