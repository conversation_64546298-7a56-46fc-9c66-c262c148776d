from app import create_app
import webbrowser
import socket
import threading
import time

app = create_app()

def open_browser():
    """Fonction pour ouvrir le navigateur après un court délai"""
    time.sleep(1.5)  # Attendre que le serveur démarre
    # Obtenir l'adresse IP locale
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    url = f"http://{local_ip}:5000"
    print(f"Application accessible à l'adresse: {url}")
    webbrowser.open(url)

if __name__ == '__main__':
    # Démarrer un thread pour ouvrir le navigateur
    threading.Thread(target=open_browser).start()
    # Démarrer le serveur Flask avec le mode debug activé
    app.run(debug=True, host='0.0.0.0', port=5000)