@echo off
echo ===== Creation du fichier executable pour Gestion des Formations =====
echo.

echo 1. Installation de PyInstaller...
pip install pyinstaller
echo.

echo 2. Creation du fichier executable...
pyinstaller --noconfirm --onefile --windowed --add-data "app/templates;app/templates" --add-data "app/static;app/static" --add-data "app/static/uploads;app/static/uploads" --add-data "migrations;migrations" --add-data "app.db;." --hidden-import email_validator --hidden-import flask --hidden-import flask_sqlalchemy --hidden-import flask_migrate --hidden-import flask_login --hidden-import flask_wtf --hidden-import wtforms --hidden-import werkzeug --hidden-import jinja2 --hidden-import sqlalchemy --hidden-import email_validator --hidden-import itsdangerous --hidden-import click --hidden-import markupsafe --name Gestion_Formation run.py
echo.

echo 3. Verification du resultat...
if exist "dist\Gestion_Formation.exe" (
    echo Le fichier executable a ete cree avec succes!
    echo Chemin: %CD%\dist\Gestion_Formation.exe
) else (
    echo ERREUR: Le fichier executable n'a pas ete cree.
    exit /b 1
)
echo.

echo ===== Processus termine =====
pause
