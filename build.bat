@echo off
echo ===== Création du fichier exécutable pour Gestion des Formations =====
echo.

echo 1. Vérification des dépendances...
pip install pyinstaller
pip install -r requirements.txt
echo.

echo 2. Création du fichier exécutable...
pyinstaller gestion_formation.spec
echo.

echo 3. Vérification du résultat...
if exist "dist\Gestion_Formation.exe" (
    echo Le fichier exécutable a été créé avec succès!
    echo Chemin: %CD%\dist\Gestion_Formation.exe
) else (
    echo ERREUR: Le fichier exécutable n'a pas été créé.
    exit /b 1
)
echo.

echo 4. Instructions pour créer l'installateur:
echo - Installez Inno Setup depuis https://jrsoftware.org/isdl.php
echo - Ouvrez le fichier gestion_formation_installer.iss avec Inno Setup
echo - Cliquez sur "Compiler" pour créer l'installateur
echo.

echo ===== Processus terminé =====
pause
