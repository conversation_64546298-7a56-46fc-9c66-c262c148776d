from app import create_app, db
from app.models import Formateur

def add_formateurs():
    app = create_app()
    with app.app_context():
        # إضافة مدربين
        if Formateur.query.count() == 0:
            formateur1 = Formateur(
                nom_prenom='<PERSON>',
                specialite='Développement Web',
                cv=True,
                diplomes=True,
                adresse='78 Rue des Écoles',
                ville='Casablanca',
                num_tel='0661234567',
                email='<EMAIL>'
            )
            
            formateur2 = Formateur(
                nom_prenom='Fatima Zahra',
                specialite='Management et Leadership',
                cv=True,
                diplomes=True,
                adresse='15 Rue des Fleurs',
                ville='Rabat',
                num_tel='0662345678',
                email='<EMAIL>'
            )
            
            db.session.add_all([formateur1, formateur2])
            db.session.commit()
            print("Added sample formateurs")
        
        print("Sample formateurs added successfully!")

if __name__ == '__main__':
    add_formateurs()
