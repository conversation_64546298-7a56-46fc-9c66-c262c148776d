<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Gestion des Formations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        overflow: hidden;
        max-width: 800px;
        width: 100%;
    }

    .left-panel {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 40px;
        text-align: center;
    }

    .right-panel {
        padding: 40px;
        background: white;
    }

    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        margin-bottom: 15px;
        font-size: 16px;
    }

    .btn-login {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        color: white;
        width: 100%;
        margin-top: 10px;
    }

    .btn-exit {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        color: white;
        width: 100%;
        margin-top: 10px;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
</style>
</head>
<body>

{% with messages = get_flashed_messages() %}
    {% if messages %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {% for message in messages %}
                {{ message }}
            {% endfor %}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    {% endif %}
{% endwith %}

<div class="login-container">
    <div class="login-card">
        <div class="row no-gutters">
            <!-- Left Panel -->
            <div class="col-md-6 left-panel">
                <div class="mb-4">
                    <img src="{{ url_for('static', filename='images/Formation-continue-1024x1024.png') }}"
                         alt="Logo" style="width: 80px; height: 80px; border-radius: 50%; background: rgba(255,255,255,0.2); padding: 15px;">
                </div>
                <h2 style="font-size: 2.5rem; margin-bottom: 20px;">Bienvenue</h2>
                <p style="font-size: 1.1rem; opacity: 0.9;">Système de Gestion des Formations<br>
                Votre plateforme professionnelle pour la gestion complète des formations</p>
            </div>

            <!-- Right Panel -->
            <div class="col-md-6 right-panel">
                <h3 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">Entrez vos identifiants pour continuer</h3>

                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div class="form-group">
                        {{ form.username(class="form-control", placeholder="Nom d'utilisateur") }}
                    </div>

                    <div class="form-group">
                        {{ form.password(class="form-control", placeholder="Mot de passe") }}
                    </div>

                    <div class="form-check mb-3">
                        {{ form.remember_me(class="form-check-input") }}
                        <label class="form-check-label">{{ form.remember_me.label.text }}</label>
                    </div>

                    {{ form.submit(class="btn btn-login") }}

                    <a href="{{ url_for('exit_application') }}" class="btn btn-exit">
                        <i class="fas fa-power-off mr-2"></i>Quitter l'Application
                    </a>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('{{ url_for("static", filename="images/Formation-continue-1024x1024.png") }}') center center no-repeat;
        background-size: 250px;
        opacity: 0.08;
        animation: float 8s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }



    .login-welcome-title {
        font-size: 2.8rem;
        font-weight: 300;
        margin-bottom: 25px;
        text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
        position: relative;
        z-index: 2;
    }

    .login-welcome-subtitle {
        font-size: 1.2rem;
        opacity: 0.95;
        line-height: 1.7;
        margin-bottom: 40px;
        position: relative;
        z-index: 2;
        font-weight: 300;
    }

    .login-right-panel {
        padding: 60px 50px;
        background: white;
        height: 100%;
        min-height: 600px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
    }

    .login-form-title {
        font-size: 2.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 15px;
        text-align: center;
    }

    .login-form-subtitle {
        color: #495057;
        text-align: center;
        margin-bottom: 45px;
        font-size: 1.1rem;
        font-weight: 400;
        line-height: 1.5;
    }

    .form-floating {
        margin-bottom: 25px;
        position: relative;
    }

    .form-floating .form-control {
        border-radius: 15px;
        border: 2px solid #e9ecef;
        padding: 20px 15px 10px 15px;
        background-color: rgba(248, 249, 250, 0.8);
        transition: all 0.3s ease;
        font-size: 1rem;
        height: 60px;
        width: 100%;
    }

    .form-floating .form-control:focus {
        border-color: #4facfe;
        box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.15);
        background-color: white;
    }

    .form-floating label {
        color: #6c757d;
        font-weight: 500;
        font-size: 1rem;
    }

    .input-group-text {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        color: white;
        border: none;
        border-radius: 15px 0 0 15px;
        padding: 15px;
    }

    .btn-login {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border: none;
        border-radius: 15px;
        padding: 15px 30px;
        font-weight: 600;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        width: 100%;
        height: 55px;
        margin-top: 20px;
    }

    .btn-login:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(39, 174, 96, 0.4);
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }

    .btn-exit {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 25px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        margin-top: 20px;
        width: 100%;
    }

    .btn-exit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
        color: white;
    }

    .remember-me-container {
        margin: 30px 0;
        text-align: center;
    }

    .form-check-input:checked {
        background-color: #2c3e50;
        border-color: #2c3e50;
    }

    .form-check-label {
        color: #6c757d;
        font-weight: 500;
    }

    .company-logo {
        width: 100px;
        height: 100px;
        margin: 0 auto 30px auto;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        padding: 20px;
        backdrop-filter: blur(15px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .company-logo:hover {
        transform: scale(1.05);
        background: rgba(255, 255, 255, 0.25);
    }

    .feature-icon {
        position: relative;
        z-index: 2;
        opacity: 0.9;
        transition: all 0.3s ease;
    }

    .feature-icon:hover {
        opacity: 1;
        transform: translateY(-3px);
    }

    @media (max-width: 768px) {
        .login-left-panel {
            padding: 40px 30px;
            height: auto;
            min-height: auto;
        }
        .login-right-panel {
            padding: 40px 30px;
            height: auto;
            min-height: auto;
        }
        .login-welcome-title {
            font-size: 2.2rem;
        }
        .login-form-title {
            font-size: 1.8rem;
        }
        .company-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }
        .login-main-card {
            width: 95%;
            height: auto;
            min-height: auto;
            max-width: none;
        }
        .feature-icon i {
            font-size: 1.5rem !important;
        }
        .login-welcome-subtitle {
            font-size: 1rem;
        }
        .login-form-subtitle {
            font-size: 1rem;
        }
    }
</style>
</head>
<body>

<!-- Flash Messages -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}

<div class="container-fluid login-container login-page-body">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="card login-main-card">
                <div class="row g-0">
                    <!-- Left Panel - Welcome Section -->
                    <div class="col-md-6 login-left-panel">
                        <div class="company-logo">
                            <img src="{{ url_for('static', filename='images/Formation-continue-1024x1024.png') }}"
                                 alt="Logo" class="img-fluid">
                        </div>

                        <h1 class="login-welcome-title">
                            <i class="fas fa-graduation-cap mr-3"></i>
                            Bienvenue
                        </h1>

                        <p class="login-welcome-subtitle">
                            Système de Gestion des Formations<br>
                            Votre plateforme professionnelle pour la gestion complète des formations
                        </p>

                        <div class="mt-4">
                            <div class="row">
                                <div class="col-4 text-center mb-3">
                                    <div class="feature-icon">
                                        <i class="fas fa-users fa-2x mb-2"></i>
                                    </div>
                                    <div class="small">Gestion des Utilisateurs</div>
                                </div>
                                <div class="col-4 text-center mb-3">
                                    <div class="feature-icon">
                                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    </div>
                                    <div class="small">Rapports Avancés</div>
                                </div>
                                <div class="col-4 text-center mb-3">
                                    <div class="feature-icon">
                                        <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                    </div>
                                    <div class="small">Sécurité Renforcée</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Panel - Login Form -->
                    <div class="col-md-6 login-right-panel">
                        <div class="text-center mb-4">
                            <h2 class="login-form-title">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                Connexion
                            </h2>
                            <p class="login-form-subtitle">Entrez vos identifiants pour continuer</p>
                        </div>

                        <form method="POST" action="">
                            {{ form.hidden_tag() }}

                            <div class="form-floating mb-3">
                                {{ form.username(class="form-control", id="floatingUsername", placeholder="Nom d'utilisateur") }}
                                <label for="floatingUsername">
                                    <i class="fas fa-user mr-2"></i>Nom d'utilisateur
                                </label>
                                {% for error in form.username.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>{{ error }}
                                </div>
                                {% endfor %}
                            </div>

                            <div class="form-floating mb-3">
                                {{ form.password(class="form-control", id="floatingPassword", placeholder="Mot de passe") }}
                                <label for="floatingPassword">
                                    <i class="fas fa-lock mr-2"></i>Mot de passe
                                </label>
                                {% for error in form.password.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>{{ error }}
                                </div>
                                {% endfor %}
                            </div>

                            <div class="remember-me-container">
                                <div class="form-check">
                                    {{ form.remember_me(class="form-check-input", id="rememberMe") }}
                                    <label class="form-check-label" for="rememberMe">
                                        <i class="fas fa-heart mr-2"></i>{{ form.remember_me.label.text }}
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                {{ form.submit(class="btn btn-login") }}

                                <a href="{{ url_for('exit_application') }}" class="btn btn-exit">
                                    <i class="fas fa-power-off mr-2"></i>
                                    Quitter l'Application
                                </a>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle mr-1"></i>
                                Système sécurisé - Tous les accès sont enregistrés
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

// Animation d'entrée pour les éléments
document.addEventListener('DOMContentLoaded', function() {
    const elements = document.querySelectorAll('.login-left-panel > *, .login-right-panel > *');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'all 0.6s ease';

        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// Effet de particules en arrière-plan
function createParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.style.position = 'fixed';
    particlesContainer.style.top = '0';
    particlesContainer.style.left = '0';
    particlesContainer.style.width = '100%';
    particlesContainer.style.height = '100%';
    particlesContainer.style.pointerEvents = 'none';
    particlesContainer.style.zIndex = '-2';
    document.body.appendChild(particlesContainer);

    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.width = Math.random() * 4 + 1 + 'px';
        particle.style.height = particle.style.width;
        particle.style.background = 'rgba(255, 255, 255, 0.1)';
        particle.style.borderRadius = '50%';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `float ${Math.random() * 10 + 5}s linear infinite`;
        particlesContainer.appendChild(particle);
    }
}

createParticles();
</script>

<!-- Bootstrap 4 JS -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
