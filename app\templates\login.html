{% extends "base.html" %}

{% block login_content %}
<style>
    body {
        background: url('{{ url_for("static", filename="images/grungy-white-background-natural-cement-stone-old-texture-as-retro-pattern-wall-conceptual-wall-banner-grunge-material-construction.svg") }}') center center fixed;
        background-size: cover;
        min-height: 100vh;
        position: relative;
    }

    body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(44, 62, 80, 0.8) 0%, rgba(26, 37, 47, 0.9) 100%);
        z-index: -1;
    }

    .login-container {
        margin-top: 3%;
        position: relative;
        z-index: 1;
    }

    .login-main-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        overflow: hidden;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .login-left-panel {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        color: white;
        padding: 60px 40px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .login-left-panel::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('{{ url_for("static", filename="images/Formation-continue-1024x1024.png") }}') center center no-repeat;
        background-size: 300px;
        opacity: 0.1;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }

    .login-illustration {
        width: 200px;
        height: 200px;
        margin-bottom: 30px;
        filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));
        animation: pulse 3s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .login-welcome-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .login-welcome-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        line-height: 1.6;
        margin-bottom: 30px;
    }

    .login-right-panel {
        padding: 60px 50px;
        background: rgba(255, 255, 255, 0.9);
    }

    .login-form-title {
        font-size: 2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 10px;
        text-align: center;
    }

    .login-form-subtitle {
        color: #6c757d;
        text-align: center;
        margin-bottom: 40px;
    }

    .form-floating {
        margin-bottom: 25px;
    }

    .form-floating .form-control {
        border-radius: 15px;
        border: 2px solid #e9ecef;
        padding: 20px 15px 10px 15px;
        background-color: rgba(248, 249, 250, 0.8);
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating .form-control:focus {
        border-color: #2c3e50;
        box-shadow: 0 0 0 4px rgba(44, 62, 80, 0.1);
        background-color: white;
    }

    .form-floating label {
        color: #6c757d;
        font-weight: 500;
    }

    .input-group-text {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        color: white;
        border: none;
        border-radius: 15px 0 0 15px;
        padding: 15px;
    }

    .btn-login {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        border: none;
        border-radius: 15px;
        padding: 15px 30px;
        font-weight: 600;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
        width: 100%;
    }

    .btn-login:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(44, 62, 80, 0.4);
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    }

    .btn-exit {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 25px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        margin-top: 20px;
        width: 100%;
    }

    .btn-exit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
        color: white;
    }

    .remember-me-container {
        margin: 30px 0;
        text-align: center;
    }

    .form-check-input:checked {
        background-color: #2c3e50;
        border-color: #2c3e50;
    }

    .form-check-label {
        color: #6c757d;
        font-weight: 500;
    }

    .company-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        padding: 15px;
        backdrop-filter: blur(10px);
    }

    @media (max-width: 768px) {
        .login-left-panel {
            padding: 40px 20px;
        }
        .login-right-panel {
            padding: 40px 30px;
        }
        .login-welcome-title {
            font-size: 2rem;
        }
        .login-illustration {
            width: 150px;
            height: 150px;
        }
    }
</style>

<div class="container-fluid login-container">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="card login-main-card">
                <div class="row g-0">
                    <!-- Left Panel - Welcome Section -->
                    <div class="col-md-6 login-left-panel">
                        <div class="company-logo">
                            <img src="{{ url_for('static', filename='images/Formation-continue-1024x1024.png') }}"
                                 alt="Logo" class="img-fluid">
                        </div>

                        <img src="{{ url_for('static', filename='images/login-illustration.svg') }}"
                             alt="Login Illustration" class="login-illustration">

                        <h1 class="login-welcome-title">
                            <i class="fas fa-graduation-cap me-3"></i>
                            Bienvenue
                        </h1>

                        <p class="login-welcome-subtitle">
                            Système de Gestion des Formations<br>
                            Connectez-vous pour accéder à votre espace de travail
                        </p>

                        <div class="mt-4">
                            <div class="d-flex justify-content-center mb-3">
                                <div class="me-4 text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <div class="small">Gestion des Utilisateurs</div>
                                </div>
                                <div class="me-4 text-center">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                    <div class="small">Rapports Détaillés</div>
                                </div>
                                <div class="text-center">
                                    <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                    <div class="small">Sécurité Avancée</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Panel - Login Form -->
                    <div class="col-md-6 login-right-panel">
                        <div class="text-center mb-4">
                            <h2 class="login-form-title">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Connexion
                            </h2>
                            <p class="login-form-subtitle">Entrez vos identifiants pour continuer</p>
                        </div>

                        <form method="POST" action="">
                            {{ form.hidden_tag() }}

                            <div class="form-floating mb-3">
                                {{ form.username(class="form-control", id="floatingUsername", placeholder="Nom d'utilisateur") }}
                                <label for="floatingUsername">
                                    <i class="fas fa-user me-2"></i>Nom d'utilisateur
                                </label>
                                {% for error in form.username.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-triangle me-1"></i>{{ error }}
                                </div>
                                {% endfor %}
                            </div>

                            <div class="form-floating mb-3">
                                {{ form.password(class="form-control", id="floatingPassword", placeholder="Mot de passe") }}
                                <label for="floatingPassword">
                                    <i class="fas fa-lock me-2"></i>Mot de passe
                                </label>
                                {% for error in form.password.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-triangle me-1"></i>{{ error }}
                                </div>
                                {% endfor %}
                            </div>

                            <div class="remember-me-container">
                                <div class="form-check">
                                    {{ form.remember_me(class="form-check-input", id="rememberMe") }}
                                    <label class="form-check-label" for="rememberMe">
                                        <i class="fas fa-heart me-2"></i>{{ form.remember_me.label.text }}
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                {{ form.submit(class="btn btn-login") }}

                                <a href="{{ url_for('exit_application') }}" class="btn btn-exit">
                                    <i class="fas fa-power-off me-2"></i>
                                    Quitter l'Application
                                </a>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Système sécurisé - Tous les accès sont enregistrés
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

// Animation d'entrée pour les éléments
document.addEventListener('DOMContentLoaded', function() {
    const elements = document.querySelectorAll('.login-left-panel > *, .login-right-panel > *');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'all 0.6s ease';

        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// Effet de particules en arrière-plan
function createParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.style.position = 'fixed';
    particlesContainer.style.top = '0';
    particlesContainer.style.left = '0';
    particlesContainer.style.width = '100%';
    particlesContainer.style.height = '100%';
    particlesContainer.style.pointerEvents = 'none';
    particlesContainer.style.zIndex = '-2';
    document.body.appendChild(particlesContainer);

    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.width = Math.random() * 4 + 1 + 'px';
        particle.style.height = particle.style.width;
        particle.style.background = 'rgba(255, 255, 255, 0.1)';
        particle.style.borderRadius = '50%';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `float ${Math.random() * 10 + 5}s linear infinite`;
        particlesContainer.appendChild(particle);
    }
}

createParticles();
</script>
{% endblock %}
