<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Gestion des Formations</title>

    <!-- Bootstrap 4 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html, body {
        height: 100%;
        overflow: hidden;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .login-page-body {
        background: url('{{ url_for("static", filename="images/grungy-white-background-natural-cement-stone-old-texture-as-retro-pattern-wall-conceptual-wall-banner-grunge-material-construction.svg") }}') center center fixed;
        background-size: cover;
        height: 100vh;
        width: 100vw;
        position: fixed;
        top: 0;
        left: 0;
    }

    .login-page-body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(240, 240, 240, 0.2) 100%);
        z-index: 1;
    }

    .login-container {
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
        padding: 0;
    }

    .login-main-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        overflow: hidden;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        max-width: 1000px;
        width: 90%;
        max-height: 85vh;
        position: relative;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .login-left-panel {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 50px 40px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .login-left-panel::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('{{ url_for("static", filename="images/Formation-continue-1024x1024.png") }}') center center no-repeat;
        background-size: 250px;
        opacity: 0.08;
        animation: float 8s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }



    .login-welcome-title {
        font-size: 2.8rem;
        font-weight: 300;
        margin-bottom: 25px;
        text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
        position: relative;
        z-index: 2;
    }

    .login-welcome-subtitle {
        font-size: 1.2rem;
        opacity: 0.95;
        line-height: 1.7;
        margin-bottom: 40px;
        position: relative;
        z-index: 2;
        font-weight: 300;
    }

    .login-right-panel {
        padding: 50px 45px;
        background: white;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .login-form-title {
        font-size: 2.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 15px;
        text-align: center;
    }

    .login-form-subtitle {
        color: #6c757d;
        text-align: center;
        margin-bottom: 45px;
        font-size: 1.1rem;
        font-weight: 400;
    }

    .form-floating {
        margin-bottom: 25px;
    }

    .form-floating .form-control {
        border-radius: 15px;
        border: 2px solid #e9ecef;
        padding: 20px 15px 10px 15px;
        background-color: rgba(248, 249, 250, 0.8);
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-floating .form-control:focus {
        border-color: #2c3e50;
        box-shadow: 0 0 0 4px rgba(44, 62, 80, 0.1);
        background-color: white;
    }

    .form-floating label {
        color: #6c757d;
        font-weight: 500;
    }

    .input-group-text {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        color: white;
        border: none;
        border-radius: 15px 0 0 15px;
        padding: 15px;
    }

    .btn-login {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border: none;
        border-radius: 15px;
        padding: 15px 30px;
        font-weight: 600;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        width: 100%;
    }

    .btn-login:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(39, 174, 96, 0.4);
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }

    .btn-exit {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        border: none;
        border-radius: 15px;
        padding: 12px 25px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        margin-top: 20px;
        width: 100%;
    }

    .btn-exit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
        color: white;
    }

    .remember-me-container {
        margin: 30px 0;
        text-align: center;
    }

    .form-check-input:checked {
        background-color: #2c3e50;
        border-color: #2c3e50;
    }

    .form-check-label {
        color: #6c757d;
        font-weight: 500;
    }

    .company-logo {
        width: 100px;
        height: 100px;
        margin-bottom: 30px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        padding: 20px;
        backdrop-filter: blur(15px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
    }

    .company-logo:hover {
        transform: scale(1.05);
        background: rgba(255, 255, 255, 0.25);
    }

    .feature-icon {
        position: relative;
        z-index: 2;
        opacity: 0.9;
        transition: all 0.3s ease;
    }

    .feature-icon:hover {
        opacity: 1;
        transform: translateY(-3px);
    }

    @media (max-width: 768px) {
        .login-left-panel {
            padding: 30px 20px;
        }
        .login-right-panel {
            padding: 30px 25px;
        }
        .login-welcome-title {
            font-size: 2.2rem;
        }
        .login-form-title {
            font-size: 1.8rem;
        }
        .company-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }
        .login-main-card {
            width: 95%;
            max-height: 95vh;
        }
        .feature-icon i {
            font-size: 1.5rem !important;
        }
    }
</style>
</head>
<body>

<!-- Flash Messages -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}

<div class="container-fluid login-container login-page-body">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="card login-main-card">
                <div class="row g-0">
                    <!-- Left Panel - Welcome Section -->
                    <div class="col-md-6 login-left-panel">
                        <div class="company-logo">
                            <img src="{{ url_for('static', filename='images/Formation-continue-1024x1024.png') }}"
                                 alt="Logo" class="img-fluid">
                        </div>

                        <h1 class="login-welcome-title">
                            <i class="fas fa-graduation-cap mr-3"></i>
                            Bienvenue
                        </h1>

                        <p class="login-welcome-subtitle">
                            Système de Gestion des Formations<br>
                            Votre plateforme professionnelle pour la gestion complète des formations
                        </p>

                        <div class="mt-4">
                            <div class="row">
                                <div class="col-4 text-center mb-3">
                                    <div class="feature-icon">
                                        <i class="fas fa-users fa-2x mb-2"></i>
                                    </div>
                                    <div class="small">Gestion des Utilisateurs</div>
                                </div>
                                <div class="col-4 text-center mb-3">
                                    <div class="feature-icon">
                                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    </div>
                                    <div class="small">Rapports Avancés</div>
                                </div>
                                <div class="col-4 text-center mb-3">
                                    <div class="feature-icon">
                                        <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                    </div>
                                    <div class="small">Sécurité Renforcée</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Panel - Login Form -->
                    <div class="col-md-6 login-right-panel">
                        <div class="text-center mb-4">
                            <h2 class="login-form-title">
                                <i class="fas fa-sign-in-alt mr-2"></i>
                                Connexion
                            </h2>
                            <p class="login-form-subtitle">Entrez vos identifiants pour continuer</p>
                        </div>

                        <form method="POST" action="">
                            {{ form.hidden_tag() }}

                            <div class="form-floating mb-3">
                                {{ form.username(class="form-control", id="floatingUsername", placeholder="Nom d'utilisateur") }}
                                <label for="floatingUsername">
                                    <i class="fas fa-user mr-2"></i>Nom d'utilisateur
                                </label>
                                {% for error in form.username.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>{{ error }}
                                </div>
                                {% endfor %}
                            </div>

                            <div class="form-floating mb-3">
                                {{ form.password(class="form-control", id="floatingPassword", placeholder="Mot de passe") }}
                                <label for="floatingPassword">
                                    <i class="fas fa-lock mr-2"></i>Mot de passe
                                </label>
                                {% for error in form.password.errors %}
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>{{ error }}
                                </div>
                                {% endfor %}
                            </div>

                            <div class="remember-me-container">
                                <div class="form-check">
                                    {{ form.remember_me(class="form-check-input", id="rememberMe") }}
                                    <label class="form-check-label" for="rememberMe">
                                        <i class="fas fa-heart mr-2"></i>{{ form.remember_me.label.text }}
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                {{ form.submit(class="btn btn-login") }}

                                <a href="{{ url_for('exit_application') }}" class="btn btn-exit">
                                    <i class="fas fa-power-off mr-2"></i>
                                    Quitter l'Application
                                </a>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle mr-1"></i>
                                Système sécurisé - Tous les accès sont enregistrés
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

// Animation d'entrée pour les éléments
document.addEventListener('DOMContentLoaded', function() {
    const elements = document.querySelectorAll('.login-left-panel > *, .login-right-panel > *');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'all 0.6s ease';

        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// Effet de particules en arrière-plan
function createParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.style.position = 'fixed';
    particlesContainer.style.top = '0';
    particlesContainer.style.left = '0';
    particlesContainer.style.width = '100%';
    particlesContainer.style.height = '100%';
    particlesContainer.style.pointerEvents = 'none';
    particlesContainer.style.zIndex = '-2';
    document.body.appendChild(particlesContainer);

    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.width = Math.random() * 4 + 1 + 'px';
        particle.style.height = particle.style.width;
        particle.style.background = 'rgba(255, 255, 255, 0.1)';
        particle.style.borderRadius = '50%';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `float ${Math.random() * 10 + 5}s linear infinite`;
        particlesContainer.appendChild(particle);
    }
}

createParticles();
</script>

<!-- Bootstrap 4 JS -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
