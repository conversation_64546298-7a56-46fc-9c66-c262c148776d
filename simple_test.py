import requests
import re

session = requests.Session()

# الحصول على csrf token
response = session.get('http://127.0.0.1:5000/login')
csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
csrf_token = csrf_match.group(1) if csrf_match else ''

# تسجيل الدخول
login_data = {
    'username': 'admin',
    'password': 'admin123',
    'csrf_token': csrf_token
}

login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
print('Login status:', login_response.status_code)

# اختبار صفحة الطباعة
print_response = session.get('http://127.0.0.1:5000/organismes/print/1')
print('Print page status:', print_response.status_code)
print('Logo found:', 'company-logo' in print_response.text)
print('Footer found:', 'company-footer' in print_response.text)
print('Company info found:', 'company-info' in print_response.text)
print('Company name found:', 'مركز التكوين المهني' in print_response.text)

if print_response.status_code == 200:
    print('✅ SUCCESS: Print page working with company branding!')
else:
    print('❌ FAILED: Print page not accessible')
