{% extends "base.html" %}

{% block title %}Importer Base de Données{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-upload me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Importer Base de Données</span>
                        </h4>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Avertissement Important :</strong>
                        <ul class="mb-0 mt-2">
                            <li>La base de données actuelle sera entièrement remplacée</li>
                            <li>Toutes les données actuelles seront définitivement perdues</li>
                            <li>Assurez-vous de créer une sauvegarde avant de continuer</li>
                            <li>Cette action est irréversible</li>
                        </ul>
                    </div>

                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    {{ form.fichier_sauvegarde.label(class="form-label") }}
                                    {{ form.fichier_sauvegarde(class="form-control") }}
                                    {% if form.fichier_sauvegarde.errors %}
                                        <div class="text-danger">
                                            {% for error in form.fichier_sauvegarde.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Formats supportés : .db, .sqlite, .zip (maximum 50 MB)
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.creer_sauvegarde_avant(class="form-check-input") }}
                                        {{ form.creer_sauvegarde_avant.label(class="form-check-label") }}
                                    </div>
                                    {% if form.creer_sauvegarde_avant.errors %}
                                        <div class="text-danger">
                                            {% for error in form.creer_sauvegarde_avant.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        (Fortement recommandé)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        {{ form.confirmer_remplacement(class="form-check-input") }}
                                        {{ form.confirmer_remplacement.label(class="form-check-label text-danger") }}
                                    </div>
                                    {% if form.confirmer_remplacement.errors %}
                                        <div class="text-danger">
                                            {% for error in form.confirmer_remplacement.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle text-info"></i> Étapes du processus d'importation
                                </h6>
                                <ol class="mb-0">
                                    <li>Créer une sauvegarde des données actuelles (si sélectionné)</li>
                                    <li>Vérifier la validité du fichier téléchargé</li>
                                    <li>Arrêter temporairement les connexions actives</li>
                                    <li>Remplacer la base de données</li>
                                    <li>Vérifier l'intégrité des nouvelles données</li>
                                    <li>Redémarrer le système</li>
                                </ol>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning btn-lg" id="importBtn" disabled>
                                <i class="fas fa-upload"></i> Commencer l'importation
                            </button>
                            <a href="{{ url_for('backup_list') }}" class="btn btn-info">
                                <i class="fas fa-list"></i> Sauvegardes
                            </a>
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-download"></i> Sauvegarde d'abord
                            </a>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Activer le bouton d'importation seulement après confirmation du remplacement
document.getElementById('confirmer_remplacement').addEventListener('change', function() {
    document.getElementById('importBtn').disabled = !this.checked;
});

document.getElementById('importBtn').addEventListener('click', function() {
    if (confirm('Êtes-vous absolument sûr de continuer ? Toutes les données actuelles seront perdues !')) {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Importation en cours...';
        this.disabled = true;
        return true;
    }
    return false;
});
</script>
{% endblock %}
