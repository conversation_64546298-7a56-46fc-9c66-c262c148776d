{% extends "base.html" %}

{% block title %}استيراد قاعدة البيانات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-upload"></i> استيراد قاعدة البيانات
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير هام:</strong> 
                        <ul class="mb-0 mt-2">
                            <li>سيتم استبدال قاعدة البيانات الحالية بالكامل</li>
                            <li>سيتم فقدان جميع البيانات الحالية نهائياً</li>
                            <li>تأكد من إنشاء نسخة احتياطية قبل المتابعة</li>
                            <li>هذا الإجراء لا يمكن التراجع عنه</li>
                        </ul>
                    </div>

                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    {{ form.fichier_sauvegarde.label(class="form-label") }}
                                    {{ form.fichier_sauvegarde(class="form-control") }}
                                    {% if form.fichier_sauvegarde.errors %}
                                        <div class="text-danger">
                                            {% for error in form.fichier_sauvegarde.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        الصيغ المدعومة: .db, .sqlite, .zip (حد أقصى 50 ميجابايت)
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.creer_sauvegarde_avant(class="form-check-input") }}
                                        {{ form.creer_sauvegarde_avant.label(class="form-check-label") }}
                                    </div>
                                    {% if form.creer_sauvegarde_avant.errors %}
                                        <div class="text-danger">
                                            {% for error in form.creer_sauvegarde_avant.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        (موصى به بشدة)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        {{ form.confirmer_remplacement(class="form-check-input") }}
                                        {{ form.confirmer_remplacement.label(class="form-check-label text-danger") }}
                                    </div>
                                    {% if form.confirmer_remplacement.errors %}
                                        <div class="text-danger">
                                            {% for error in form.confirmer_remplacement.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle text-info"></i> خطوات عملية الاستيراد
                                </h6>
                                <ol class="mb-0">
                                    <li>إنشاء نسخة احتياطية من البيانات الحالية (إذا تم تحديدها)</li>
                                    <li>التحقق من صحة الملف المرفوع</li>
                                    <li>إيقاف الاتصالات النشطة مؤقتاً</li>
                                    <li>استبدال قاعدة البيانات</li>
                                    <li>التحقق من سلامة البيانات الجديدة</li>
                                    <li>إعادة تشغيل النظام</li>
                                </ol>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning btn-lg" id="importBtn" disabled>
                                <i class="fas fa-upload"></i> بدء عملية الاستيراد
                            </button>
                            <a href="{{ url_for('backup_list') }}" class="btn btn-info">
                                <i class="fas fa-list"></i> النسخ الاحتياطية
                            </a>
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-download"></i> نسخة احتياطية أولاً
                            </a>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الاستيراد فقط عند تأكيد الاستبدال
document.getElementById('confirmer_remplacement').addEventListener('change', function() {
    document.getElementById('importBtn').disabled = !this.checked;
});

document.getElementById('importBtn').addEventListener('click', function() {
    if (confirm('هل أنت متأكد تماماً من المتابعة؟ سيتم فقدان جميع البيانات الحالية!')) {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاستيراد...';
        this.disabled = true;
        return true;
    }
    return false;
});
</script>
{% endblock %}
