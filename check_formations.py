from app import create_app, db
from app.models import Formation, Organisme

def check_formations():
    app = create_app()
    with app.app_context():
        formations = Formation.query.all()
        print('Formations:')
        for formation in formations:
            organisme = Organisme.query.get(formation.organisme_id)
            organisme_name = organisme.raison_sociale if organisme else "Unknown"
            print(f'ID: {formation.id}, Theme: {formation.theme}, Date: {formation.date}, Organisme: {organisme_name}')

if __name__ == '__main__':
    check_formations()
