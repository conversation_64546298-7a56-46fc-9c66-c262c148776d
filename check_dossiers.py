from app import create_app, db
from app.models import DossierTechnique, FicheInscription, Domaine, Theme, Organisme

def check_dossiers():
    app = create_app()
    with app.app_context():
        dossiers = DossierTechnique.query.all()
        print('Dossiers Techniques:')
        for dossier in dossiers:
            fiche = FicheInscription.query.get(dossier.fiche_inscription_id)
            domaine = Domaine.query.get(dossier.domaine_id)
            theme = Theme.query.get(dossier.theme_id)
            organisme = Organisme.query.get(dossier.organisme_formation_id)
            
            fiche_name = fiche.raison_sociale if fiche else "Unknown"
            domaine_name = domaine.nom if domaine else "Unknown"
            theme_name = theme.nom if theme else "Unknown"
            organisme_name = organisme.raison_sociale if organisme else "Unknown"
            
            print(f'ID: {dossier.id}, Fiche: {fiche_name}, Domaine: {domaine_name}, Theme: {theme_name}, Organisme: {organisme_name}')
            print(f'Objectif: {dossier.objectif[:50]}..., Type: {dossier.type_formation}, Coût: {dossier.cout_formation_ht}')

if __name__ == '__main__':
    check_dossiers()
