from app import create_app, db
from app.models import User
from datetime import datetime, timezone

def create_admin():
    app = create_app()
    with app.app_context():
        # Create admin user
        admin = User(
            username='admin',
            email='<EMAIL>',
            is_admin=True,
            nom_complet='Administrateur',
            date_creation=datetime.now(timezone.utc),
            perm_fiche_inscription=True,
            perm_dossier_technique=True,
            perm_dossier_remboursement=True,
            perm_organisme=True,
            perm_formateur=True,
            perm_agenda=True,
            perm_domaine_theme=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print('Admin user created successfully!')

if __name__ == '__main__':
    create_admin()
