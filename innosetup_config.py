"""
Script pour faciliter la création de l'installateur avec Inno Setup.
Ce script vérifie si Inno Setup est installé et lance la compilation.
"""

import os
import sys
import subprocess
import winreg

def find_inno_setup():
    """Trouve le chemin d'installation d'Inno Setup"""
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Inno Setup 6_is1") as key:
            return winreg.QueryValueEx(key, "InstallLocation")[0]
    except:
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Inno Setup 6_is1") as key:
                return winreg.QueryValueEx(key, "InstallLocation")[0]
        except:
            return None

def main():
    print("===== Création de l'installateur pour Gestion des Formations =====")
    
    # Vérifier si le fichier exécutable existe
    if not os.path.exists(os.path.join("dist", "Gestion_Formation.exe")):
        print("ERREUR: Le fichier exécutable n'existe pas.")
        print("Veuillez d'abord créer le fichier exécutable avec PyInstaller.")
        return 1
    
    # Trouver Inno Setup
    inno_setup_path = find_inno_setup()
    if not inno_setup_path:
        print("ERREUR: Inno Setup n'est pas installé.")
        print("Veuillez installer Inno Setup depuis https://jrsoftware.org/isdl.php")
        return 1
    
    iscc_path = os.path.join(inno_setup_path, "ISCC.exe")
    if not os.path.exists(iscc_path):
        print(f"ERREUR: ISCC.exe non trouvé dans {inno_setup_path}")
        return 1
    
    # Compiler l'installateur
    print("\nCréation de l'installateur...")
    subprocess.check_call([iscc_path, "gestion_formation_installer.iss"])
    
    # Vérifier si l'installateur a été créé
    installer_path = os.path.join("Output", "Gestion_Formation_Setup.exe")
    if os.path.exists(installer_path):
        print("\nL'installateur a été créé avec succès!")
        print(f"Chemin: {os.path.abspath(installer_path)}")
    else:
        print("\nERREUR: L'installateur n'a pas été créé.")
        return 1
    
    print("\n===== Processus terminé =====")
    return 0

if __name__ == "__main__":
    sys.exit(main())
