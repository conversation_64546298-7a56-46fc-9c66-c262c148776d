from app import create_app, db
from app.models import DossierTechnique, FicheInscription, Domaine, Theme, Organisme
from datetime import datetime, timezone

def add_dossiers():
    app = create_app()
    with app.app_context():
        # إضافة ملفات تقنية
        if DossierTechnique.query.count() == 0:
            fiches = FicheInscription.query.all()
            domaines = Domaine.query.all()
            themes = Theme.query.all()
            organismes = Organisme.query.all()

            if fiches and domaines and themes and organismes:
                dossier1 = DossierTechnique(
                    fiche_inscription_id=fiches[0].id,
                    domaine_id=domaines[0].id,
                    theme_id=themes[0].id,
                    objectif="Améliorer les compétences en développement web",
                    contenu_indicatif="HTML, CSS, JavaScript, React",
                    organisme_formation_id=organismes[0].id,
                    num_cnss_organisme=organismes[0].num_cnss,
                    type_formation="Présentiel",
                    cout_formation_ht=15000.0,
                    effectif_global=10,
                    nombre_cadres=3,
                    nombre_employes=7,
                    nombre_ouvriers=0,
                    conforme=True,
                    depot_physique=True,
                    date_depot=datetime.now(timezone.utc),
                    validation=True
                )

                dossier2 = DossierTechnique(
                    fiche_inscription_id=fiches[1].id,
                    domaine_id=domaines[1].id,
                    theme_id=themes[2].id if len(themes) > 2 else themes[0].id,
                    objectif="Développer les compétences en leadership",
                    contenu_indicatif="Communication, Gestion d'équipe, Prise de décision",
                    organisme_formation_id=organismes[0].id,
                    num_cnss_organisme=organismes[0].num_cnss,
                    type_formation="Présentiel",
                    cout_formation_ht=20000.0,
                    effectif_global=5,
                    nombre_cadres=5,
                    nombre_employes=0,
                    nombre_ouvriers=0,
                    conforme=True,
                    depot_physique=True,
                    date_depot=datetime.now(timezone.utc),
                    validation=True
                )

                db.session.add_all([dossier1, dossier2])
                db.session.commit()
                print("Added sample dossiers techniques")
            else:
                print("Missing required data (fiches, domaines, themes, or organismes)")

        print("Sample dossiers added successfully!")

if __name__ == '__main__':
    add_dossiers()
