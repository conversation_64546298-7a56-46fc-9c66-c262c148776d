{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-file-alt me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Fiches d'Inscription</span>
                        </h4>
                        <a href="{{ url_for('new_fiche_inscription') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nouvelle Fiche
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <div class="col-md-4">
            <form action="{{ url_for('search_fiches') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher une fiche..." value="{{ query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Liste des Fiches d'Inscription</h5>
                </div>
                <div class="card-body">
                    {% if fiches %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Raison Sociale</th>
                                    <th>Date d'Inscription</th>
                                    <th>Email</th>
                                    <th>Téléphone</th>
                                    <th>Éligible</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fiche in fiches %}
                                <tr>
                                    <td>{{ fiche.id }}</td>
                                    <td>{{ fiche.raison_sociale }}</td>
                                    <td>{{ fiche.date_inscription.strftime('%d/%m/%Y') if fiche.date_inscription else 'N/A' }}</td>
                                    <td>{{ fiche.email }}</td>
                                    <td>{{ fiche.tel_entreprise }}</td>
                                    <td>
                                        {% if fiche.eligible %}
                                        <span class="badge bg-success">Oui</span>
                                        {% else %}
                                        <span class="badge bg-danger">Non</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_fiche_inscription', id=fiche.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('print_fiche_inscription', id=fiche.id) }}" class="btn btn-sm btn-info" title="Imprimer" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                                    data-delete-url="{{ url_for('delete_fiche_inscription', id=fiche.id) }}"
                                                    data-delete-message="Êtes-vous sûr de vouloir supprimer cette fiche?"
                                                    data-use-modal="true"
                                                    data-delete-title="Confirmer la suppression">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        Aucune fiche d'inscription n'a été trouvée.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
