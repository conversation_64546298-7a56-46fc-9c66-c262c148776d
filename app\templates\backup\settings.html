{% extends "base.html" %}

{% block title %}إعدادات النسخ الاحتياطي{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-cog"></i> إعدادات النسخ الاحتياطي التلقائي
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        {{ form.activer_sauvegarde_auto(class="form-check-input") }}
                                        {{ form.activer_sauvegarde_auto.label(class="form-check-label") }}
                                    </div>
                                    {% if form.activer_sauvegarde_auto.errors %}
                                        <div class="text-danger">
                                            {% for error in form.activer_sauvegarde_auto.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.frequence_sauvegarde.label(class="form-label") }}
                                    {{ form.frequence_sauvegarde(class="form-control") }}
                                    {% if form.frequence_sauvegarde.errors %}
                                        <div class="text-danger">
                                            {% for error in form.frequence_sauvegarde.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.heure_sauvegarde.label(class="form-label") }}
                                    {{ form.heure_sauvegarde(class="form-control") }}
                                    {% if form.heure_sauvegarde.errors %}
                                        <div class="text-danger">
                                            {% for error in form.heure_sauvegarde.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        الوقت المفضل لإجراء النسخ الاحتياطي التلقائي
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.nombre_sauvegardes_garder.label(class="form-label") }}
                                    {{ form.nombre_sauvegardes_garder(class="form-control") }}
                                    {% if form.nombre_sauvegardes_garder.errors %}
                                        <div class="text-danger">
                                            {% for error in form.nombre_sauvegardes_garder.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        عدد النسخ الاحتياطية التي سيتم الاحتفاظ بها (النسخ الأقدم ستحذف تلقائياً)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    {{ form.dossier_sauvegarde.label(class="form-label") }}
                                    {{ form.dossier_sauvegarde(class="form-control") }}
                                    {% if form.dossier_sauvegarde.errors %}
                                        <div class="text-danger">
                                            {% for error in form.dossier_sauvegarde.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        المجلد الذي سيتم حفظ النسخ الاحتياطية فيه
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        {{ form.activer_notifications(class="form-check-input") }}
                                        {{ form.activer_notifications.label(class="form-check-label") }}
                                    </div>
                                    {% if form.activer_notifications.errors %}
                                        <div class="text-danger">
                                            {% for error in form.activer_notifications.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.email_notifications.label(class="form-label") }}
                                    {{ form.email_notifications(class="form-control") }}
                                    {% if form.email_notifications.errors %}
                                        <div class="text-danger">
                                            {% for error in form.email_notifications.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        البريد الإلكتروني لإرسال تنبيهات النسخ الاحتياطي
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>ملاحظة:</strong> النسخ الاحتياطي التلقائي يتطلب إعداد مهام مجدولة على الخادم.
                            يرجى التواصل مع مدير النظام لتفعيل هذه الخاصية.
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                            <a href="{{ url_for('backup_list') }}" class="btn btn-info">
                                <i class="fas fa-list"></i> عرض النسخ الاحتياطية
                            </a>
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-download"></i> نسخة احتياطية يدوية
                            </a>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
