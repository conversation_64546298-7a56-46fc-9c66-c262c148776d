{% extends "base.html" %}

{% block title %}Paramètres de Sauvegarde{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-cog"></i> Paramètres de Sauvegarde Automatique
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        {{ form.backup_automatique(class="form-check-input") }}
                                        {{ form.backup_automatique.label(class="form-check-label") }}
                                    </div>
                                    {% if form.backup_automatique.errors %}
                                        <div class="text-danger">
                                            {% for error in form.backup_automatique.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.frequence_backup.label(class="form-label") }}
                                    {{ form.frequence_backup(class="form-control") }}
                                    {% if form.frequence_backup.errors %}
                                        <div class="text-danger">
                                            {% for error in form.frequence_backup.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.heure_backup.label(class="form-label") }}
                                    {{ form.heure_backup(class="form-control") }}
                                    {% if form.heure_backup.errors %}
                                        <div class="text-danger">
                                            {% for error in form.heure_backup.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Heure préférée pour effectuer la sauvegarde automatique
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.nombre_max_backups.label(class="form-label") }}
                                    {{ form.nombre_max_backups(class="form-control") }}
                                    {% if form.nombre_max_backups.errors %}
                                        <div class="text-danger">
                                            {% for error in form.nombre_max_backups.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Nombre de sauvegardes à conserver (les plus anciennes seront supprimées automatiquement)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    {{ form.dossier_backup.label(class="form-label") }}
                                    {{ form.dossier_backup(class="form-control") }}
                                    {% if form.dossier_backup.errors %}
                                        <div class="text-danger">
                                            {% for error in form.dossier_backup.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Dossier où seront sauvegardées les sauvegardes
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        {{ form.compression(class="form-check-input") }}
                                        {{ form.compression.label(class="form-check-label") }}
                                    </div>
                                    {% if form.compression.errors %}
                                        <div class="text-danger">
                                            {% for error in form.compression.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        {{ form.notification_email(class="form-check-input") }}
                                        {{ form.notification_email.label(class="form-check-label") }}
                                    </div>
                                    {% if form.notification_email.errors %}
                                        <div class="text-danger">
                                            {% for error in form.notification_email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.email_notification.label(class="form-label") }}
                                    {{ form.email_notification(class="form-control") }}
                                    {% if form.email_notification.errors %}
                                        <div class="text-danger">
                                            {% for error in form.email_notification.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Adresse e-mail pour recevoir les notifications de sauvegarde
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Note :</strong> La sauvegarde automatique nécessite la configuration de tâches planifiées sur le serveur.
                            Veuillez contacter l'administrateur système pour activer cette fonctionnalité.
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Enregistrer les paramètres
                            </button>
                            <a href="{{ url_for('backup_list') }}" class="btn btn-info">
                                <i class="fas fa-list"></i> Voir les sauvegardes
                            </a>
                            <a href="{{ url_for('manual_backup') }}" class="btn btn-success">
                                <i class="fas fa-download"></i> Sauvegarde manuelle
                            </a>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
