#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3

def fix_database():
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    # إضافة الأعمدة الجديدة
    columns_to_add = [
        'perm_users',
        'perm_company_info', 
        'perm_activity_log',
        'perm_backup',
        'perm_reports'
    ]

    for column in columns_to_add:
        try:
            cursor.execute(f'ALTER TABLE user ADD COLUMN {column} BOOLEAN DEFAULT 0')
            print(f'✅ تم إضافة عمود {column}')
        except sqlite3.OperationalError as e:
            if 'duplicate column name' in str(e):
                print(f'⚠️ عمود {column} موجود بالفعل')
            else:
                print(f'❌ خطأ في إضافة عمود {column}: {e}')

    # تحديث المستخدم الإداري
    cursor.execute('''
        UPDATE user 
        SET perm_users = 1, 
            perm_company_info = 1, 
            perm_activity_log = 1, 
            perm_backup = 1, 
            perm_reports = 1 
        WHERE username = 'admin'
    ''')

    conn.commit()
    conn.close()

    print('✅ تم تحديث قاعدة البيانات بنجاح!')

if __name__ == '__main__':
    fix_database()
