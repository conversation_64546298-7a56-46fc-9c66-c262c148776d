from app import create_app, db
from app.models import User

def check_users():
    app = create_app()
    with app.app_context():
        users = User.query.all()
        print('Users:')
        for user in users:
            print(f'ID: {user.id}, Username: {user.username}, Email: {user.email}, Is Admin: {user.is_admin}')
            print(f'Permissions: fiche_inscription={user.perm_fiche_inscription}, dossier_technique={user.perm_dossier_technique}, dossier_remboursement={user.perm_dossier_remboursement}, organisme={user.perm_organisme}, formateur={user.perm_formateur}, agenda={user.perm_agenda}, domaine_theme={user.perm_domaine_theme}')

if __name__ == '__main__':
    check_users()
