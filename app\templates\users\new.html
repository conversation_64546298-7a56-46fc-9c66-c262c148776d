{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-user-plus"></i> Nouvel Utilisateur</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations de l'Utilisateur</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.username.label(class="form-control-label") }}
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.email.label(class="form-control-label") }}
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.nom_complet.label(class="form-control-label") }}
                            {{ form.nom_complet(class="form-control") }}
                            {% if form.nom_complet.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nom_complet.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.password.label(class="form-control-label") }}
                            {{ form.password(class="form-control") }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            {{ form.is_admin(class="form-check-input") }}
                            {{ form.is_admin.label(class="form-check-label") }}
                            {% if form.is_admin.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_admin.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Section des Permissions Améliorée -->
                <div class="card mb-4 permissions-card">
                    <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 font-weight-bold">
                                <i class="fas fa-shield-alt mr-2"></i>
                                Gestion des Permissions
                            </h5>
                            <div class="permission-controls">
                                <button type="button" class="btn btn-sm btn-outline-light mr-2" id="selectAllPermissions">
                                    <i class="fas fa-check-double mr-1"></i>Tout Sélectionner
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-light" id="clearAllPermissions">
                                    <i class="fas fa-times mr-1"></i>Tout Désélectionner
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <div class="permissions-grid">
                            <!-- Groupe 1: Gestion des Dossiers -->
                            <div class="permission-group mb-4">
                                <h6 class="permission-group-title">
                                    <i class="fas fa-folder-open text-primary mr-2"></i>
                                    Gestion des Dossiers
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="permission-item">
                                            <div class="custom-control custom-switch">
                                                {{ form.perm_fiche_inscription(class="custom-control-input", id="perm_fiche_inscription") }}
                                                <label class="custom-control-label" for="perm_fiche_inscription">
                                                    <div class="permission-info">
                                                        <div class="permission-title">Fiche d'inscription</div>
                                                        <div class="permission-desc">Créer, modifier et consulter les fiches d'inscription</div>
                                                    </div>
                                                </label>
                                            </div>
                                            {% if form.perm_fiche_inscription.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.perm_fiche_inscription.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="permission-item">
                                            <div class="custom-control custom-switch">
                                                {{ form.perm_dossier_technique(class="custom-control-input", id="perm_dossier_technique") }}
                                                <label class="custom-control-label" for="perm_dossier_technique">
                                                    <div class="permission-info">
                                                        <div class="permission-title">Dossier technique</div>
                                                        <div class="permission-desc">Gérer les dossiers techniques des formations</div>
                                                    </div>
                                                </label>
                                            </div>
                                            {% if form.perm_dossier_technique.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.perm_dossier_technique.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="permission-item">
                                            <div class="custom-control custom-switch">
                                                {{ form.perm_dossier_remboursement(class="custom-control-input", id="perm_dossier_remboursement") }}
                                                <label class="custom-control-label" for="perm_dossier_remboursement">
                                                    <div class="permission-info">
                                                        <div class="permission-title">Dossier de remboursement</div>
                                                        <div class="permission-desc">Traiter les demandes de remboursement</div>
                                                    </div>
                                                </label>
                                            </div>
                                            {% if form.perm_dossier_remboursement.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.perm_dossier_remboursement.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Groupe 2: Gestion des Ressources -->
                            <div class="permission-group mb-4">
                                <h6 class="permission-group-title">
                                    <i class="fas fa-users text-success mr-2"></i>
                                    Gestion des Ressources
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="permission-item">
                                            <div class="custom-control custom-switch">
                                                {{ form.perm_organisme(class="custom-control-input", id="perm_organisme") }}
                                                <label class="custom-control-label" for="perm_organisme">
                                                    <div class="permission-info">
                                                        <div class="permission-title">Organismes</div>
                                                        <div class="permission-desc">Gérer les organismes de formation</div>
                                                    </div>
                                                </label>
                                            </div>
                                            {% if form.perm_organisme.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.perm_organisme.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="permission-item">
                                            <div class="custom-control custom-switch">
                                                {{ form.perm_formateur(class="custom-control-input", id="perm_formateur") }}
                                                <label class="custom-control-label" for="perm_formateur">
                                                    <div class="permission-info">
                                                        <div class="permission-title">Formateurs</div>
                                                        <div class="permission-desc">Gérer les profils des formateurs</div>
                                                    </div>
                                                </label>
                                            </div>
                                            {% if form.perm_formateur.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.perm_formateur.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="permission-item">
                                            <div class="custom-control custom-switch">
                                                {{ form.perm_agenda(class="custom-control-input", id="perm_agenda") }}
                                                <label class="custom-control-label" for="perm_agenda">
                                                    <div class="permission-info">
                                                        <div class="permission-title">Agenda des formateurs</div>
                                                        <div class="permission-desc">Planifier et gérer les rendez-vous</div>
                                                    </div>
                                                </label>
                                            </div>
                                            {% if form.perm_agenda.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.perm_agenda.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Groupe 3: Configuration -->
                            <div class="permission-group">
                                <h6 class="permission-group-title">
                                    <i class="fas fa-cogs text-warning mr-2"></i>
                                    Configuration
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="permission-item">
                                            <div class="custom-control custom-switch">
                                                {{ form.perm_domaine_theme(class="custom-control-input", id="perm_domaine_theme") }}
                                                <label class="custom-control-label" for="perm_domaine_theme">
                                                    <div class="permission-info">
                                                        <div class="permission-title">Domaines et Thèmes</div>
                                                        <div class="permission-desc">Configurer les domaines et thèmes de formation</div>
                                                    </div>
                                                </label>
                                            </div>
                                            {% if form.perm_domaine_theme.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.perm_domaine_theme.errors %}
                                                        <span>{{ error }}</span>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('users') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .permissions-card {
        border: none;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-radius: 15px;
        overflow: hidden;
    }

    .permission-group {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        border-left: 4px solid #007bff;
    }

    .permission-group-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.1rem;
    }

    .permission-item {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .permission-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-color: #007bff;
    }

    .permission-info {
        margin-left: 10px;
    }

    .permission-title {
        font-weight: 600;
        color: #495057;
        font-size: 0.95rem;
    }

    .permission-desc {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 2px;
    }

    .custom-switch .custom-control-label::before {
        width: 2.5rem;
        height: 1.4rem;
        border-radius: 1rem;
        background-color: #dee2e6;
        border: none;
    }

    .custom-switch .custom-control-label::after {
        width: 1.1rem;
        height: 1.1rem;
        border-radius: 50%;
        background-color: white;
        top: 0.15rem;
        left: 0.15rem;
    }

    .custom-switch .custom-control-input:checked ~ .custom-control-label::before {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .custom-switch .custom-control-input:checked ~ .custom-control-label::after {
        transform: translateX(1.1rem);
    }

    .permission-controls .btn {
        border-radius: 20px;
        font-size: 0.85rem;
        padding: 5px 15px;
    }

    .permissions-grid {
        position: relative;
    }

    .permission-item.active {
        border-color: #28a745;
        background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
    }

    @media (max-width: 768px) {
        .permission-controls {
            margin-top: 10px;
        }

        .permission-controls .btn {
            font-size: 0.75rem;
            padding: 4px 10px;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Fonction pour sélectionner toutes les permissions
    $('#selectAllPermissions').click(function() {
        $('.custom-control-input[id^="perm_"]').prop('checked', true).trigger('change');
        $(this).addClass('btn-success').removeClass('btn-outline-light');
        $('#clearAllPermissions').removeClass('btn-danger').addClass('btn-outline-light');
    });

    // Fonction pour désélectionner toutes les permissions
    $('#clearAllPermissions').click(function() {
        $('.custom-control-input[id^="perm_"]').prop('checked', false).trigger('change');
        $(this).addClass('btn-danger').removeClass('btn-outline-light');
        $('#selectAllPermissions').removeClass('btn-success').addClass('btn-outline-light');
    });

    // Animation lors du changement d'état des permissions
    $('.custom-control-input[id^="perm_"]').change(function() {
        const permissionItem = $(this).closest('.permission-item');
        if ($(this).is(':checked')) {
            permissionItem.addClass('active');
            // Animation de succès
            permissionItem.animate({
                backgroundColor: '#d4edda'
            }, 200).animate({
                backgroundColor: 'white'
            }, 200);
        } else {
            permissionItem.removeClass('active');
        }

        // Mettre à jour les boutons de contrôle
        updateControlButtons();
    });

    // Fonction pour mettre à jour l'état des boutons de contrôle
    function updateControlButtons() {
        const totalPermissions = $('.custom-control-input[id^="perm_"]').length;
        const checkedPermissions = $('.custom-control-input[id^="perm_"]:checked').length;

        if (checkedPermissions === totalPermissions) {
            $('#selectAllPermissions').addClass('btn-success').removeClass('btn-outline-light');
            $('#clearAllPermissions').removeClass('btn-danger').addClass('btn-outline-light');
        } else if (checkedPermissions === 0) {
            $('#clearAllPermissions').addClass('btn-danger').removeClass('btn-outline-light');
            $('#selectAllPermissions').removeClass('btn-success').addClass('btn-outline-light');
        } else {
            $('#selectAllPermissions').removeClass('btn-success').addClass('btn-outline-light');
            $('#clearAllPermissions').removeClass('btn-danger').addClass('btn-outline-light');
        }
    }

    // Initialiser l'état des boutons au chargement
    updateControlButtons();

    // Animation d'entrée pour les éléments de permission
    $('.permission-item').each(function(index) {
        $(this).css('opacity', '0').delay(index * 100).animate({
            opacity: 1
        }, 300);
    });
});
</script>
{% endblock %}
