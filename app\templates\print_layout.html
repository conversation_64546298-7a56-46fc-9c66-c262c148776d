<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #ddd;
            padding-bottom: 20px;
        }
        .company-logo {
            max-height: 80px;
            max-width: 200px;
            margin-bottom: 15px;
        }
        .company-info {
            margin-bottom: 15px;
        }
        .company-name {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .company-slogan {
            font-size: 14px;
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }
        .company-contact {
            font-size: 12px;
            color: #777;
            line-height: 1.4;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
            margin-top: 15px;
        }
        .header p {
            font-size: 14px;
            color: #666;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 2px solid #ddd;
            padding-top: 20px;
        }
        .company-footer {
            margin-bottom: 10px;
            font-weight: 500;
            color: #555;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-section h2 {
            font-size: 18px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            margin-right: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        {% if company_info %}
            {% if company_info.logo_path %}
                <img src="{{ url_for('static', filename=company_info.logo_path) }}"
                     alt="Logo de l'entreprise"
                     class="company-logo">
            {% endif %}

            <div class="company-info">
                <div class="company-name">{{ company_info.nom_entreprise }}</div>
                {% if company_info.slogan %}
                    <div class="company-slogan">{{ company_info.slogan }}</div>
                {% endif %}

                <div class="company-contact">
                    {% if company_info.adresse %}{{ company_info.adresse }}<br>{% endif %}
                    {% if company_info.telephone %}Tél: {{ company_info.telephone }}{% endif %}
                    {% if company_info.telephone and company_info.fax %} | {% endif %}
                    {% if company_info.fax %}Fax: {{ company_info.fax }}{% endif %}
                    {% if (company_info.telephone or company_info.fax) and company_info.email %}<br>{% endif %}
                    {% if company_info.email %}Email: {{ company_info.email }}{% endif %}
                    {% if company_info.site_web %}<br>Site: {{ company_info.site_web }}{% endif %}
                </div>
            </div>
        {% endif %}

        <h1>{{ title }}</h1>
        <p>Imprimé le {{ now.strftime('%d/%m/%Y à %H:%M') }}</p>
    </div>

    <div class="content">
        {% block content %}{% endblock %}
    </div>

    <div class="footer">
        {% if company_info and company_info.pied_de_page %}
            <div class="company-footer">{{ company_info.pied_de_page }}</div>
        {% endif %}
        <p>Document imprimé par {{ current_user.nom_complet }} le {{ now.strftime('%d/%m/%Y à %H:%M') }}</p>
    </div>

    <div class="no-print text-center mt-4">
        <button class="btn btn-primary" onclick="window.print()">Imprimer</button>
        <a href="{{ back_url }}" class="btn btn-secondary">Retour</a>
    </div>

    <script>
        // Imprimer automatiquement lorsque la page est chargée
        window.onload = function() {
            // Attendre un peu pour que la page soit complètement rendue
            setTimeout(function() {
                // window.print();
            }, 500);
        };
    </script>
</body>
</html>
