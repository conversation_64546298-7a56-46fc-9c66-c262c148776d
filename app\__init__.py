from flask import Flask
from config import Config
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect

# تهيئة الإضافات
db = SQLAlchemy()
login_manager = LoginManager()
login_manager.login_view = 'login'
csrf = CSRFProtect()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # تهيئة الإضافات مع التطبيق
    db.init_app(app)
    login_manager.init_app(app)
    csrf.init_app(app)

    # إضافة csrf_token إلى السياق العام للقوالب
    @app.context_processor
    def inject_csrf_token():
        from flask_wtf.csrf import generate_csrf
        return dict(csrf_token=generate_csrf)

    # تسجيل المسارات
    with app.app_context():
        from app.routes import (
            index, login, logout, dashboard,
            formations, new_formation, edit_formation, delete_formation,
            dossiers_techniques, new_dossier_technique, edit_dossier_technique, delete_dossier_technique,
            remboursements, new_remboursement, edit_remboursement, delete_remboursement,
            organismes, new_organisme, edit_organisme, delete_organisme,
            formateurs, new_formateur, edit_formateur, delete_formateur,
            users, new_user, edit_user, delete_user,
            domaines_themes, new_domaine, edit_domaine, delete_domaine,
            new_theme, edit_theme, delete_theme,
            agenda_formateurs, new_agenda, edit_agenda, delete_agenda,
            generate_report,
            fiches_inscription, new_fiche_inscription, edit_fiche_inscription, delete_fiche_inscription,
            eligibilite_ofppt,
            search_fiches, search_dossiers, search_remboursements, search_organismes, search_formateurs, search_domaines_themes, search_users,
            print_fiche_inscription, print_dossier_technique, print_remboursement, print_organisme, print_formateur, print_agenda,
            company_info, activity_log, backup_settings, manual_backup, backup_list, import_database
        )

        # Routes principales
        app.add_url_rule('/', 'index', index)
        app.add_url_rule('/index', 'index', index)
        app.add_url_rule('/login', 'login', login, methods=['GET', 'POST'])
        app.add_url_rule('/logout', 'logout', logout)
        app.add_url_rule('/dashboard', 'dashboard', dashboard)

        # Routes pour les formations
        app.add_url_rule('/formations', 'formations', formations)
        app.add_url_rule('/formations/new', 'new_formation', new_formation, methods=['GET', 'POST'])
        app.add_url_rule('/formations/edit/<int:id>', 'edit_formation', edit_formation, methods=['GET', 'POST'])
        app.add_url_rule('/formations/delete/<int:id>', 'delete_formation', delete_formation, methods=['POST'])

        # Routes pour les dossiers techniques
        app.add_url_rule('/dossiers-techniques', 'dossiers_techniques', dossiers_techniques)
        app.add_url_rule('/dossiers-techniques/new', 'new_dossier_technique', new_dossier_technique, methods=['GET', 'POST'])
        app.add_url_rule('/dossiers-techniques/edit/<int:id>', 'edit_dossier_technique', edit_dossier_technique, methods=['GET', 'POST'])
        app.add_url_rule('/dossiers-techniques/delete/<int:id>', 'delete_dossier_technique', delete_dossier_technique, methods=['POST'])

        # Routes pour les remboursements
        app.add_url_rule('/remboursements', 'remboursements', remboursements)
        app.add_url_rule('/remboursements/new', 'new_remboursement', new_remboursement, methods=['GET', 'POST'])
        app.add_url_rule('/remboursements/edit/<int:id>', 'edit_remboursement', edit_remboursement, methods=['GET', 'POST'])
        app.add_url_rule('/remboursements/delete/<int:id>', 'delete_remboursement', delete_remboursement, methods=['POST'])

        # Routes pour les organismes
        app.add_url_rule('/organismes', 'organismes', organismes)
        app.add_url_rule('/organismes/new', 'new_organisme', new_organisme, methods=['GET', 'POST'])
        app.add_url_rule('/organismes/edit/<int:id>', 'edit_organisme', edit_organisme, methods=['GET', 'POST'])
        app.add_url_rule('/organismes/delete/<int:id>', 'delete_organisme', delete_organisme, methods=['POST'])

        # Routes pour les formateurs
        app.add_url_rule('/formateurs', 'formateurs', formateurs)
        app.add_url_rule('/formateurs/new', 'new_formateur', new_formateur, methods=['GET', 'POST'])
        app.add_url_rule('/formateurs/edit/<int:id>', 'edit_formateur', edit_formateur, methods=['GET', 'POST'])
        app.add_url_rule('/formateurs/delete/<int:id>', 'delete_formateur', delete_formateur, methods=['POST'])

        # Routes pour les utilisateurs
        app.add_url_rule('/users', 'users', users)
        app.add_url_rule('/users/new', 'new_user', new_user, methods=['GET', 'POST'])
        app.add_url_rule('/users/edit/<int:id>', 'edit_user', edit_user, methods=['GET', 'POST'])
        app.add_url_rule('/users/delete/<int:id>', 'delete_user', delete_user, methods=['POST'])

        # Routes pour les domaines et thèmes
        app.add_url_rule('/domaines-themes', 'domaines_themes', domaines_themes)
        app.add_url_rule('/domaines/new', 'new_domaine', new_domaine, methods=['GET', 'POST'])
        app.add_url_rule('/domaines/edit/<int:id>', 'edit_domaine', edit_domaine, methods=['GET', 'POST'])
        app.add_url_rule('/domaines/delete/<int:id>', 'delete_domaine', delete_domaine, methods=['POST'])

        app.add_url_rule('/themes/new', 'new_theme', new_theme, methods=['GET', 'POST'])
        app.add_url_rule('/themes/edit/<int:id>', 'edit_theme', edit_theme, methods=['GET', 'POST'])
        app.add_url_rule('/themes/delete/<int:id>', 'delete_theme', delete_theme, methods=['POST'])

        # Routes pour l'agenda des formateurs
        app.add_url_rule('/agenda', 'agenda_formateurs', agenda_formateurs)
        app.add_url_rule('/agenda/new', 'new_agenda', new_agenda, methods=['GET', 'POST'])
        app.add_url_rule('/agenda/edit/<int:id>', 'edit_agenda', edit_agenda, methods=['GET', 'POST'])
        app.add_url_rule('/agenda/delete/<int:id>', 'delete_agenda', delete_agenda, methods=['POST'])

        # Route pour générer des rapports
        app.add_url_rule('/reports/<string:type>', 'generate_report', generate_report)

        # Routes pour les fiches d'inscription
        app.add_url_rule('/fiches-inscription', 'fiches_inscription', fiches_inscription)
        app.add_url_rule('/fiches-inscription/new', 'new_fiche_inscription', new_fiche_inscription, methods=['GET', 'POST'])
        app.add_url_rule('/fiches-inscription/edit/<int:id>', 'edit_fiche_inscription', edit_fiche_inscription, methods=['GET', 'POST'])
        app.add_url_rule('/fiches-inscription/delete/<int:id>', 'delete_fiche_inscription', delete_fiche_inscription)

        # Route pour l'éligibilité OFPPT
        app.add_url_rule('/eligibilite-ofppt', 'eligibilite_ofppt', eligibilite_ofppt)

        # Routes pour la recherche
        app.add_url_rule('/fiches-inscription/search', 'search_fiches', search_fiches)
        app.add_url_rule('/dossiers-techniques/search', 'search_dossiers', search_dossiers)
        app.add_url_rule('/remboursements/search', 'search_remboursements', search_remboursements)
        app.add_url_rule('/organismes/search', 'search_organismes', search_organismes)
        app.add_url_rule('/formateurs/search', 'search_formateurs', search_formateurs)
        app.add_url_rule('/domaines-themes/search', 'search_domaines_themes', search_domaines_themes)
        app.add_url_rule('/users/search', 'search_users', search_users)

        # Routes pour l'impression
        app.add_url_rule('/fiches-inscription/print/<int:id>', 'print_fiche_inscription', print_fiche_inscription)
        app.add_url_rule('/dossiers-techniques/print/<int:id>', 'print_dossier_technique', print_dossier_technique)
        app.add_url_rule('/remboursements/print/<int:id>', 'print_remboursement', print_remboursement)
        app.add_url_rule('/organismes/print/<int:id>', 'print_organisme', print_organisme)
        app.add_url_rule('/formateurs/print/<int:id>', 'print_formateur', print_formateur)
        app.add_url_rule('/agenda/print/<int:id>', 'print_agenda', print_agenda)

        # Routes pour les nouvelles fonctionnalités
        app.add_url_rule('/company/info', 'company_info', company_info, methods=['GET', 'POST'])
        app.add_url_rule('/activity-log', 'activity_log', activity_log, methods=['GET', 'POST'])
        app.add_url_rule('/backup/settings', 'backup_settings', backup_settings, methods=['GET', 'POST'])
        app.add_url_rule('/backup/manual', 'manual_backup', manual_backup, methods=['GET', 'POST'])
        app.add_url_rule('/backup/list', 'backup_list', backup_list)
        app.add_url_rule('/backup/import', 'import_database', import_database, methods=['GET', 'POST'])

    return app
