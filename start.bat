@echo off
echo Demarrage de l'application de gestion des formations...
echo.

REM Essayer python d'abord
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python trouve, demarrage de l'application...
    python run.py
    goto :end
)

REM Si python ne fonctionne pas, essayer le chemin complet
echo Python non trouve dans PATH, essai du chemin complet...
if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    echo Python trouve dans le chemin complet, demarrage...
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" run.py
) else (
    echo ERREUR: Python non trouve!
    echo Veuillez verifier que Python est installe dans:
    echo C:\Users\<USER>\AppData\Local\Programs\Python\Python311\
    echo.
    echo Ou ajoutez Python au PATH systeme.
    pause
)

:end
