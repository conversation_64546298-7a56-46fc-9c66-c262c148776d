/**
 * Enhanced Delete Handler
 * Provides a safe and reliable way to delete items
 */

// Function to create hidden delete form
function createDeleteForm(url, csrfToken) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    form.style.display = 'none';

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    return form;
}

// Function to handle delete with confirmation
function handleDelete(url, message, csrfToken) {
    // Default confirmation message
    const confirmMessage = message || 'Êtes-vous sûr de vouloir supprimer cet élément ?';

    if (confirm(confirmMessage)) {
        // Create hidden form and submit
        const form = createDeleteForm(url, csrfToken);
        form.submit();
        return true;
    }
    return false;
}

// Function to handle delete with modal
function handleDeleteWithModal(url, title, message, csrfToken) {
    // Create confirmation modal
    const modalHtml = `
        <div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteConfirmModalLabel">${title || 'Confirmer la suppression'}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>${message || 'Êtes-vous sûr de vouloir supprimer cet élément ?'}</p>
                        <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Cette action ne peut pas être annulée !</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Supprimer</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('deleteConfirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add new modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    $('#deleteConfirmModal').modal('show');

    // Handle confirmation button click
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        $('#deleteConfirmModal').modal('hide');
        const form = createDeleteForm(url, csrfToken);
        form.submit();
    });

    // Remove modal when closed
    $('#deleteConfirmModal').on('hidden.bs.modal', function() {
        this.remove();
    });
}

// Initialize delete handlers when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    if (!csrfToken) {
        console.warn('CSRF token not found. Delete operations may fail.');
    }

    // Handle all delete buttons
    document.querySelectorAll('[data-delete-url]').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const url = this.getAttribute('data-delete-url');
            const message = this.getAttribute('data-delete-message');
            const useModal = this.getAttribute('data-use-modal') === 'true';
            const title = this.getAttribute('data-delete-title');

            if (useModal) {
                handleDeleteWithModal(url, title, message, csrfToken);
            } else {
                handleDelete(url, message, csrfToken);
            }
        });
    });

    // Handle traditional delete buttons (for compatibility with old code)
    document.querySelectorAll('a[href*="/delete/"], a[href*="/delete_"]').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const url = this.href;
            const message = this.getAttribute('onclick')?.match(/confirm\('([^']+)'\)/)?.[1] ||
                           'Êtes-vous sûr de vouloir supprimer cet élément ?';

            handleDelete(url, message, csrfToken);
        });
    });
});

// Helper function to delete item with AJAX
function deleteWithAjax(url, csrfToken, onSuccess, onError) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken
        },
        body: `csrf_token=${encodeURIComponent(csrfToken)}`
    })
    .then(response => {
        if (response.ok) {
            if (onSuccess) onSuccess(response);
        } else {
            throw new Error('Network response was not ok');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        if (onError) onError(error);
    });
}

// Export functions for global use
window.handleDelete = handleDelete;
window.handleDeleteWithModal = handleDeleteWithModal;
window.deleteWithAjax = deleteWithAjax;
