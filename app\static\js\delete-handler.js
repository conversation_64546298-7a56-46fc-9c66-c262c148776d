/**
 * معالج الحذف المحسن - Enhanced Delete Handler
 * يوفر طريقة آمنة وموثوقة لحذف العناصر
 */

// دالة لإنشاء نموذج حذف مخفي
function createDeleteForm(url, csrfToken) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    form.style.display = 'none';
    
    // إضافة CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);
    
    document.body.appendChild(form);
    return form;
}

// دالة لمعالجة الحذف مع تأكيد
function handleDelete(url, message, csrfToken) {
    // رسالة التأكيد الافتراضية
    const confirmMessage = message || 'هل أنت متأكد من أنك تريد حذف هذا العنصر؟';
    
    if (confirm(confirmMessage)) {
        // إنشاء نموذج مخفي وإرساله
        const form = createDeleteForm(url, csrfToken);
        form.submit();
        return true;
    }
    return false;
}

// دالة لمعالجة الحذف مع modal
function handleDeleteWithModal(url, title, message, csrfToken) {
    // إنشاء modal للتأكيد
    const modalHtml = `
        <div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteConfirmModalLabel">${title || 'تأكيد الحذف'}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>${message || 'هل أنت متأكد من أنك تريد حذف هذا العنصر؟'}</p>
                        <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> هذا الإجراء لا يمكن التراجع عنه!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // إزالة modal سابق إن وجد
    const existingModal = document.getElementById('deleteConfirmModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // إضافة modal جديد
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // إظهار modal
    $('#deleteConfirmModal').modal('show');
    
    // معالجة النقر على زر التأكيد
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        $('#deleteConfirmModal').modal('hide');
        const form = createDeleteForm(url, csrfToken);
        form.submit();
    });
    
    // إزالة modal عند الإغلاق
    $('#deleteConfirmModal').on('hidden.bs.modal', function() {
        this.remove();
    });
}

// تهيئة معالجات الحذف عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على CSRF token من meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    
    if (!csrfToken) {
        console.warn('CSRF token not found. Delete operations may fail.');
    }
    
    // معالجة جميع أزرار الحذف
    document.querySelectorAll('[data-delete-url]').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const url = this.getAttribute('data-delete-url');
            const message = this.getAttribute('data-delete-message');
            const useModal = this.getAttribute('data-use-modal') === 'true';
            const title = this.getAttribute('data-delete-title');
            
            if (useModal) {
                handleDeleteWithModal(url, title, message, csrfToken);
            } else {
                handleDelete(url, message, csrfToken);
            }
        });
    });
    
    // معالجة أزرار الحذف التقليدية (للتوافق مع الكود القديم)
    document.querySelectorAll('a[href*="/delete/"], a[href*="/delete_"]').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const url = this.href;
            const message = this.getAttribute('onclick')?.match(/confirm\('([^']+)'\)/)?.[1] || 
                           'هل أنت متأكد من أنك تريد حذف هذا العنصر؟';
            
            handleDelete(url, message, csrfToken);
        });
    });
});

// دالة مساعدة لحذف عنصر بـ AJAX
function deleteWithAjax(url, csrfToken, onSuccess, onError) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken
        },
        body: `csrf_token=${encodeURIComponent(csrfToken)}`
    })
    .then(response => {
        if (response.ok) {
            if (onSuccess) onSuccess(response);
        } else {
            throw new Error('Network response was not ok');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        if (onError) onError(error);
    });
}

// تصدير الدوال للاستخدام العام
window.handleDelete = handleDelete;
window.handleDeleteWithModal = handleDeleteWithModal;
window.deleteWithAjax = deleteWithAjax;
