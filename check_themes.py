from app import create_app, db
from app.models import Theme, Domain<PERSON>

def check_themes():
    app = create_app()
    with app.app_context():
        themes = Theme.query.all()
        print('Themes:')
        for theme in themes:
            domaine = Domaine.query.get(theme.domaine_id)
            domaine_name = domaine.nom if domaine else "Unknown"
            print(f'ID: {theme.id}, Nom: {theme.nom}, Domaine: {domaine_name}')

if __name__ == '__main__':
    check_themes()
