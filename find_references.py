import os

def find_references(directory, keywords):
    references = {}
    for keyword in keywords:
        references[keyword] = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py') or file.endswith('.html'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        for keyword in keywords:
                            if keyword in content:
                                references[keyword].append(file_path)
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return references

# البحث عن الإشارات إلى "accueil" و "formation"
references = find_references('app', ['accueil', 'formation'])

print("الملفات التي تحتوي على إشارات إلى 'accueil':")
for file in references['accueil']:
    print(file)

print("\nالملفات التي تحتوي على إشارات إلى 'formation':")
for file in references['formation']:
    print(file)
