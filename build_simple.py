"""
Script simple para crear un ejecutable con PyInstaller
"""

import os
import sys
import subprocess

def main():
    print("===== Creación del ejecutable para Gestión de Formaciones =====")
    
    # Ejecutar PyInstaller con opciones básicas
    print("\nEjecutando PyInstaller...")
    try:
        subprocess.check_call(["pyinstaller", "--onefile", "--windowed", "run.py"])
        print("PyInstaller completado con éxito!")
    except subprocess.CalledProcessError as e:
        print(f"Error al ejecutar PyInstaller: {e}")
        return 1
    
    # Verificar si se creó el ejecutable
    if os.path.exists(os.path.join("dist", "run.exe")):
        print("\nEl ejecutable se ha creado correctamente!")
        print(f"Ruta: {os.path.abspath(os.path.join('dist', 'run.exe'))}")
    else:
        print("\nERROR: No se pudo crear el ejecutable.")
        return 1
    
    print("\n===== Proceso completado =====")
    return 0

if __name__ == "__main__":
    sys.exit(main())
