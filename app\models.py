from app import db, login_manager
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from datetime import datetime, timezone

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), index=True, unique=True)
    email = db.Column(db.String(120), index=True, unique=True)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.<PERSON>, default=False)
    nom_complet = db.Column(db.String(100))
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    derniere_connexion = db.Column(db.DateTime)

    # Permissions
    perm_fiche_inscription = db.Column(db.<PERSON>olean, default=False)
    perm_dossier_technique = db.Column(db.<PERSON>, default=False)
    perm_dossier_remboursement = db.Column(db.<PERSON>, default=False)
    perm_organisme = db.Column(db.<PERSON>, default=False)
    perm_formateur = db.Column(db.Boolean, default=False)
    perm_agenda = db.Column(db.Boolean, default=False)
    perm_domaine_theme = db.Column(db.Boolean, default=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def has_permission(self, permission_name):
        """Vérifie si l'utilisateur a une permission spécifique"""
        if self.is_admin:
            return True

        permission_attr = f"perm_{permission_name}"
        if hasattr(self, permission_attr):
            return bool(getattr(self, permission_attr))
        return False

@login_manager.user_loader
def load_user(id):
    return User.query.get(int(id))



# نموذج بيانات التسجيل (Fiche de l'inscription)
class FicheInscription(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    date_inscription = db.Column(db.DateTime, index=True, default=lambda: datetime.now(timezone.utc)) # DATE DE L'INSCRIPTION
    raison_sociale = db.Column(db.String(255)) # RAISON SOCIALE
    tel_entreprise = db.Column(db.String(50)) # TEL DE ENTREPRISE
    fax_entreprise = db.Column(db.String(50)) # FAX DE ENTREPRISE
    email = db.Column(db.String(120)) # EMAIL
    mot_de_passe_email = db.Column(db.String(128)) # MOT DE PASSE EMAIL
    patente = db.Column(db.String(100)) # PATENTE
    identifiant_fiscale = db.Column(db.String(100)) # IDENTIFIANT FISCALE
    num_rc = db.Column(db.String(100)) # N° RC
    num_cnss = db.Column(db.String(100)) # N° CNSS
    eligible = db.Column(db.Boolean, default=False) # ELIGIBLE
    ice = db.Column(db.String(100)) # ICE
    mot_de_passe_ice = db.Column(db.String(128)) # MOT DE PASSE ICE
    nombre_cadres = db.Column(db.Integer) # Nombre des CADRES
    nombre_employes = db.Column(db.Integer) # Nombre des EMPLOYES
    nombre_ouvriers = db.Column(db.Integer) # Nombre des OUVRIERS
    validation = db.Column(db.Boolean, default=False) # VALIDATION
    date_validation = db.Column(db.DateTime) # DATE DE VALIDATION
    depot_physique = db.Column(db.Boolean, default=False) # DÉPÔT PHYSIQUE
    date_depot = db.Column(db.DateTime) # DATE DE DÉPÔT
    piece_jointe = db.Column(db.String(255)) # PIÈCE JOINTE (يمكن تخزين مسار الملف هنا)

    # علاقة مع DossierTechnique (واحدة لواحد أو واحدة لمتعدد حسب التصميم)
    # dossier_technique = db.relationship('DossierTechnique', backref='fiche_inscription', uselist=False)


# نموذج الملف الفني (Dossier technique)
class DossierTechnique(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    # ربط مع FicheInscription
    fiche_inscription_id = db.Column(db.Integer, db.ForeignKey('fiche_inscription.id'))
    # ENTREPRISE و Numéro de série سيتم جلبهما من FicheInscription المرتبطة

    domaine_id = db.Column(db.Integer, db.ForeignKey('domaine.id')) # DOMAINES
    theme_id = db.Column(db.Integer, db.ForeignKey('theme.id')) # THÉME

    objectif = db.Column(db.Text) # Objectif (compétence visée)
    contenu_indicatif = db.Column(db.Text) # Contenu indicatif
    organisme_formation_id = db.Column(db.Integer, db.ForeignKey('organisme.id')) # Organisme de Formation
    num_cnss_organisme = db.Column(db.String(100)) # N° CNSS de l’organisme (يمكن جلبه من Organisme المرتبط)
    type_formation = db.Column(db.String(100)) # Type de formation
    cout_formation_ht = db.Column(db.Float) # Coût de la Formation HT
    effectif_global = db.Column(db.Integer) # Effectif global de la population concerné
    nombre_cadres = db.Column(db.Integer) # Nombre des Cadres
    nombre_employes = db.Column(db.Integer) # Nombre des Employes
    nombre_ouvriers = db.Column(db.Integer) # Nombre des Ouvriers
    conforme = db.Column(db.Boolean, default=False) # CONFORME
    depot_physique = db.Column(db.Boolean, default=False) # DÉPÔT PHYSIQUE (5 jours avant la formation)
    date_depot = db.Column(db.DateTime) # DATE DE DÉPÔT
    validation = db.Column(db.Boolean, default=False) # VALIDATION
    piece_jointe = db.Column(db.String(255)) # PIÈCE JOINTE

    # علاقات
    fiche_inscription = db.relationship('FicheInscription', backref=db.backref('dossiers_techniques', lazy=True))
    domaine = db.relationship('Domaine', backref=db.backref('dossiers_techniques', lazy=True))
    theme = db.relationship('Theme', backref=db.backref('dossiers_techniques', lazy=True))
    organisme_formation = db.relationship('Organisme', backref=db.backref('dossiers_techniques', lazy=True))


# نموذج الملف المالي (Dossier de remboursement)
class DossierRemboursement(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    organisme_id = db.Column(db.Integer, db.ForeignKey('organisme.id')) # ORGANISME
    # Numéro de série Organisme سيتم جلبه من Organisme المرتبط

    formateur_id = db.Column(db.Integer, db.ForeignKey('formateur.id')) # FORMATEUR
    fiche_inscription_id = db.Column(db.Integer, db.ForeignKey('fiche_inscription.id')) # ENTREPRISE (ربط مع FicheInscription)
    # Numéro de série Entreprise سيتم جلبه من FicheInscription المرتبطة

    theme = db.Column(db.String(255)) # THEME (يمكن ربطه بنموذج Theme بدلاً من نص)
    date = db.Column(db.DateTime) # DATE (تاريخ الاسترداد؟ تاريخ الدورة؟ يحتاج توضيح)
    contrat = db.Column(db.String(255)) # CONTRAT (رقم العقد؟)
    f2 = db.Column(db.Boolean, default=False) # F2
    liste_de_presence = db.Column(db.Boolean, default=False) # LISTE_DE_PRÉSENCE
    fiche_synthetique_evaluation_formateur = db.Column(db.Boolean, default=False) # FICHE_SYNTHÉTIQUE_D'ÉVALUATION_DE_FORMATEUR
    f4 = db.Column(db.Boolean, default=False) # F4
    facturation = db.Column(db.String(50)) # FACTURATION (par action / globale)
    mode_de_reglement = db.Column(db.String(50)) # MODE_DE_REGLEMENT (par cheque / par virement)
    m6 = db.Column(db.Boolean, default=False) # M6
    piece_jointe = db.Column(db.String(255)) # PIECE JOINTE

    # علاقات
    organisme = db.relationship('Organisme', backref=db.backref('dossiers_remboursement', lazy=True))
    formateur = db.relationship('Formateur', backref=db.backref('dossiers_remboursement', lazy=True))
    fiche_inscription = db.relationship('FicheInscription', backref=db.backref('dossiers_remboursement', lazy=True))


# نموذج المنظمة (Organisme)
class Organisme(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    raison_sociale = db.Column(db.String(255)) # Raison Sociale
    forme_juridique = db.Column(db.String(100)) # Forme juridique
    date_creation = db.Column(db.DateTime) # Date de création
    nom_prenom_gerant = db.Column(db.String(255)) # Nom et prénom du gérant
    adresse = db.Column(db.String(255)) # Adresse
    ville = db.Column(db.String(100)) # Ville
    telephone = db.Column(db.String(50)) # Téléphone
    fax = db.Column(db.String(50)) # Fax
    email = db.Column(db.String(120)) # Email
    patente = db.Column(db.String(100)) # Patente
    identifiant_fiscal = db.Column(db.String(100)) # Identifiant fiscal
    num_rc = db.Column(db.String(100)) # N° RC
    num_cnss = db.Column(db.String(100)) # N° CNSS
    piece_jointe = db.Column(db.String(255)) # Pièce jointe


# نموذج المدرب (Formateur)
class Formateur(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    nom_prenom = db.Column(db.String(255)) # Nom et prénom du FORMATEUR
    specialite = db.Column(db.String(255)) # SPECIALITE
    cv = db.Column(db.Boolean, default=False) # C.V
    diplomes = db.Column(db.Boolean, default=False) # Diplômes
    adresse = db.Column(db.String(255)) # ADRESSE
    ville = db.Column(db.String(100)) # VILLE
    num_tel = db.Column(db.String(50)) # N° DE TEL
    email = db.Column(db.String(120)) # EMAIL
    piece_jointe = db.Column(db.String(255)) # PIÈCE JOINTE

    # علاقة مع AgendaFormateur
    agendas = db.relationship('AgendaFormateur', backref='formateur', lazy='dynamic')


# نموذج المجالات (DOMAINES)
class Domaine(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    nom = db.Column(db.String(255), unique=True) # Nom du DOMAINE

    # علاقة مع Theme
    themes = db.relationship('Theme', backref='domaine', lazy='dynamic')


# نموذج المواضيع (THÉME)
class Theme(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    nom = db.Column(db.String(255), unique=True) # Nom du THÉME
    domaine_id = db.Column(db.Integer, db.ForeignKey('domaine.id')) # ربط مع المجال


# نموذج أجندة المدربين (Agenda des formateurs)
class AgendaFormateur(db.Model):
    id = db.Column(db.Integer, primary_key=True) # Numéro de série Automatique
    formateur_id = db.Column(db.Integer, db.ForeignKey('formateur.id')) # ربط مع المدرب
    date_rendezvous = db.Column(db.DateTime, index=True) # تاريخ الموعد
    description = db.Column(db.Text) # وصف الموعد

    # يمكن إضافة قيود للتأكد من عدم تكرار المواعيد لنفس المدرب في نفس الوقت
    __table_args__ = (db.UniqueConstraint('formateur_id', 'date_rendezvous', name='_formateur_date_uc'),)

# نموذج التقارير (Rapport)
class Rapport(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    titre = db.Column(db.String(255), nullable=False)
    type_rapport = db.Column(db.String(50), nullable=False)  # fiche_inscription, dossier_technique, etc.
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    contenu = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    # علاقة مع المستخدم
    user = db.relationship('User', backref=db.backref('rapports', lazy=True))

    def __repr__(self):
        return f'<Rapport {self.titre}>'

# نموذج الإشعارات (Notification)
class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    message = db.Column(db.String(255), nullable=False)
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    lu = db.Column(db.Boolean, default=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    # علاقة مع المستخدم
    user = db.relationship('User', backref=db.backref('notifications', lazy=True))

    def __repr__(self):
        return f'<Notification {self.id}>'

# Correct Formation model definition based on app/routes.py usage
class Formation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    theme = db.Column(db.String(100), nullable=False)
    date = db.Column(db.Date, nullable=False)
    organisme_id = db.Column(db.Integer, db.ForeignKey('organisme.id'), nullable=False)
    organisme = db.relationship('Organisme', backref='formations')


# نموذج معلومات الشركة (Company Information)
class CompanyInfo(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nom_entreprise = db.Column(db.String(255), nullable=False)  # اسم الشركة
    slogan = db.Column(db.String(500))  # شعار الشركة
    adresse = db.Column(db.Text)  # العنوان
    telephone = db.Column(db.String(50))  # الهاتف
    fax = db.Column(db.String(50))  # الفاكس
    email = db.Column(db.String(120))  # البريد الإلكتروني
    site_web = db.Column(db.String(255))  # الموقع الإلكتروني
    logo_path = db.Column(db.String(255))  # مسار الشعار
    pied_de_page = db.Column(db.Text)  # نص أسفل الصفحة
    couleur_principale = db.Column(db.String(7), default='#007bff')  # اللون الأساسي (hex)
    couleur_secondaire = db.Column(db.String(7), default='#6c757d')  # اللون الثانوي
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    date_modification = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<CompanyInfo {self.nom_entreprise}>'


# نموذج تتبع حركة المستخدمين (User Activity Log)
class UserActivityLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    nom_utilisateur = db.Column(db.String(100), nullable=False)  # اسم المستخدم
    type_action = db.Column(db.String(100), nullable=False)  # نوع العمل (إضافة، تعديل، حذف، عرض)
    module_concerne = db.Column(db.String(100), nullable=False)  # الوحدة المعنية (فيش التسجيل، الملف الفني، إلخ)
    description_action = db.Column(db.Text)  # وصف العمل
    adresse_ip = db.Column(db.String(45))  # عنوان IP
    user_agent = db.Column(db.String(500))  # معلومات المتصفح
    date_action = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), index=True)
    heure_action = db.Column(db.Time, default=lambda: datetime.now(timezone.utc).time())

    # علاقة مع المستخدم
    user = db.relationship('User', backref=db.backref('activity_logs', lazy=True))

    def __repr__(self):
        return f'<UserActivityLog {self.nom_utilisateur} - {self.type_action}>'


# نموذج إعدادات النسخ الاحتياطي (Backup Settings)
class BackupSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    backup_automatique = db.Column(db.Boolean, default=False)  # تفعيل النسخ الاحتياطي التلقائي
    frequence_backup = db.Column(db.String(20), default='daily')  # يومي، أسبوعي، شهري
    heure_backup = db.Column(db.Time, default=lambda: datetime.strptime('02:00', '%H:%M').time())  # وقت النسخ الاحتياطي
    dossier_backup = db.Column(db.String(500))  # مجلد حفظ النسخ الاحتياطية
    nombre_max_backups = db.Column(db.Integer, default=10)  # عدد النسخ الاحتياطية المحفوظة
    compression = db.Column(db.Boolean, default=True)  # ضغط النسخ الاحتياطية
    notification_email = db.Column(db.Boolean, default=False)  # إرسال إشعار بالبريد الإلكتروني
    email_notification = db.Column(db.String(120))  # البريد الإلكتروني للإشعارات
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    date_modification = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<BackupSettings {self.frequence_backup}>'


# نموذج سجل النسخ الاحتياطي (Backup Log)
class BackupLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nom_fichier = db.Column(db.String(255), nullable=False)  # اسم ملف النسخة الاحتياطية
    chemin_fichier = db.Column(db.String(500), nullable=False)  # مسار الملف
    taille_fichier = db.Column(db.BigInteger)  # حجم الملف بالبايت
    type_backup = db.Column(db.String(20), nullable=False)  # manual, automatic
    statut = db.Column(db.String(20), default='success')  # success, failed, in_progress
    message_erreur = db.Column(db.Text)  # رسالة الخطأ في حالة الفشل
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # المستخدم الذي قام بالنسخ (للنسخ اليدوي)
    date_creation = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), index=True)
    duree_backup = db.Column(db.Integer)  # مدة النسخ الاحتياطي بالثواني

    # علاقة مع المستخدم
    user = db.relationship('User', backref=db.backref('backup_logs', lazy=True))

    def __repr__(self):
        return f'<BackupLog {self.nom_fichier} - {self.statut}>'
