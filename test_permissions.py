#!/usr/bin/env python
# -*- coding: utf-8 -*-

from run import app
from app.models import User

def test_permissions():
    with app.app_context():
        # التحقق من المستخدم الإداري
        admin_user = User.query.filter_by(username='admin').first()
        if admin_user:
            print('صلاحيات المستخدم الإداري:')
            print('=' * 40)
            print(f'✅ Fiche inscription: {admin_user.perm_fiche_inscription}')
            print(f'✅ Dossier technique: {admin_user.perm_dossier_technique}')
            print(f'✅ Dossier remboursement: {admin_user.perm_dossier_remboursement}')
            print(f'✅ Organisme: {admin_user.perm_organisme}')
            print(f'✅ Formateur: {admin_user.perm_formateur}')
            print(f'✅ Agenda: {admin_user.perm_agenda}')
            print(f'✅ Domaine theme: {admin_user.perm_domaine_theme}')
            print(f'✅ Users: {admin_user.perm_users}')
            print(f'✅ Company info: {admin_user.perm_company_info}')
            print(f'✅ Activity log: {admin_user.perm_activity_log}')
            print(f'✅ Backup: {admin_user.perm_backup}')
            print(f'✅ Reports: {admin_user.perm_reports}')
            
            # عد الصلاحيات المفعلة
            permissions_count = sum([
                admin_user.perm_fiche_inscription,
                admin_user.perm_dossier_technique,
                admin_user.perm_dossier_remboursement,
                admin_user.perm_organisme,
                admin_user.perm_formateur,
                admin_user.perm_agenda,
                admin_user.perm_domaine_theme,
                admin_user.perm_users,
                admin_user.perm_company_info,
                admin_user.perm_activity_log,
                admin_user.perm_backup,
                admin_user.perm_reports
            ])
            
            print(f'\nإجمالي الصلاحيات المفعلة: {permissions_count}/12')
            
            if permissions_count == 12:
                print('🎉 جميع الصلاحيات مفعلة بنجاح!')
            else:
                print('⚠️ بعض الصلاحيات غير مفعلة')
        else:
            print('❌ لم يتم العثور على المستخدم الإداري')

if __name__ == '__main__':
    test_permissions()
