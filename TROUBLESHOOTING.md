# دليل حل المشاكل - Troubleshooting Guide

## مشكلة: Internal Server Error في Flask

### الأعراض:
- جميع مشاريع Flask تعطي خطأ "Internal Server Error"
- حتى المشاريع التي كانت تعمل سابقاً توقفت عن العمل

### الحلول المطبقة بنجاح:

#### 1. تنظيف ملفات Cache
```cmd
# حذف مجلدات __pycache__
Get-ChildItem -Path . -Recurse -Name "__pycache__" | ForEach-Object { Remove-Item -Path $_ -Recurse -Force }

# حذف ملفات .pyc
Get-ChildItem -Path . -Recurse -Filter "*.pyc" | Remove-Item -Force
```

#### 2. إعادة إنشاء البيئة الافتراضية
```cmd
# حذف البيئة القديمة
Remove-Item -Path "venv" -Recurse -Force

# إنشاء بيئة جديدة
python -m venv venv

# تفعيل البيئة
.\venv\Scripts\activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

#### 3. إصلاح قاعدة البيانات
```cmd
# حذف قاعدة البيانات التالفة
Remove-Item app.db

# إعادة إنشاء قاعدة البيانات
python init_db.py
```

## مشكلة: OperationalError - no such table

### السبب:
- قاعدة البيانات غير موجودة أو تالفة
- الجداول لم يتم إنشاؤها بشكل صحيح

### الحل:
1. حذف ملف قاعدة البيانات الحالي
2. تشغيل `python init_db.py` لإعادة إنشاء قاعدة البيانات
3. التأكد من صحة مسار قاعدة البيانات في `config.py`

## بيانات تسجيل الدخول الافتراضية:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## ملفات مهمة:
- `init_db.py`: إنشاء قاعدة البيانات والبيانات الأولية
- `config.py`: إعدادات التطبيق ومسار قاعدة البيانات
- `start.bat`: تشغيل التطبيق بسهولة
- `requirements.txt`: قائمة المتطلبات

## نصائح الوقاية:
1. استخدم البيئة الافتراضية دائماً
2. احتفظ بنسخة احتياطية من قاعدة البيانات
3. لا تحدث Python أو الحزم بشكل مفاجئ
4. نظف ملفات Cache عند حدوث مشاكل غريبة
