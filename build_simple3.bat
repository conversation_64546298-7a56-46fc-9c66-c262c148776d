@echo off
echo ===== Creation du fichier executable pour Gestion des Formations =====
echo.

echo 1. Creation du fichier executable...
pyinstaller --noconfirm --collect-all flask --collect-all flask_login --collect-all flask_wtf --collect-all email_validator --collect-all werkzeug --collect-all jinja2 --collect-all itsdangerous --collect-all click --collect-all markupsafe --collect-all wtforms --name Gestion_Formation run.py
echo.

echo 2. Verification du resultat...
if exist "dist\Gestion_Formation" (
    echo Le dossier executable a ete cree avec succes!
    echo Chemin: %CD%\dist\Gestion_Formation
) else (
    echo ERREUR: Le dossier executable n'a pas ete cree.
    exit /b 1
)
echo.

echo ===== Processus termine =====
pause
