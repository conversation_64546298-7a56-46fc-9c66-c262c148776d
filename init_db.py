from app import create_app, db
from app.models import User, Domaine, Theme, Organisme, Formateur
from werkzeug.security import generate_password_hash
from datetime import datetime, timezone

app = create_app()

def init_db():
    with app.app_context():
        # إنشاء قاعدة البيانات
        db.create_all()

        # إنشاء مستخدم افتراضي للاختبار
        if User.query.filter_by(username='admin').first() is None:
            user = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                nom_complet='Administrateur',
                date_creation=datetime.now(timezone.utc),
                perm_fiche_inscription=True,
                perm_dossier_technique=True,
                perm_dossier_remboursement=True,
                perm_organisme=True,
                perm_formateur=True,
                perm_agenda=True,
                perm_domaine_theme=True
            )
            user.set_password('admin123')
            db.session.add(user)

            # إضافة بعض المجالات الافتراضية
            domaines = [
                'Management et Ressources Humaines',
                'Finance et Comptabilité',
                'Informatique et Digital',
                'Production et Qualité'
            ]

            for nom_domaine in domaines:
                if not Domaine.query.filter_by(nom=nom_domaine).first():
                    domaine = Domaine(nom=nom_domaine)
                    db.session.add(domaine)

            # إضافة منظمة تكوين افتراضية
            if not Organisme.query.filter_by(raison_sociale='Centre de Formation Test').first():
                organisme = Organisme(
                    raison_sociale='Centre de Formation Test',
                    forme_juridique='SARL',
                    date_creation=datetime.now(timezone.utc),
                    nom_prenom_gerant='Jean Dupont',
                    adresse='123 Rue de la Formation',
                    ville='Casablanca',
                    telephone='0522000000',
                    email='<EMAIL>'
                )
                db.session.add(organisme)

            db.session.commit()
            print("Created default admin user and initial data")

if __name__ == '__main__':
    init_db()
    print("Database initialized successfully")
