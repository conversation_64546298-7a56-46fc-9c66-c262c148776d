{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-sitemap me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Domaines et Thèmes</span>
                        </h4>
                        <div class="btn-group" role="group">
                            <a href="{{ url_for('new_domaine') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-plus"></i> Nouveau Domaine
                            </a>
                            <a href="{{ url_for('new_theme') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-plus"></i> Nouveau Thème
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_domaines_themes') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un domaine ou un thème..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Liste des Domaines</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for domaine in domaines %}
                                <tr>
                                    <td>{{ domaine.id }}</td>
                                    <td>{{ domaine.nom }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_domaine', id=domaine.id) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                                    data-delete-url="{{ url_for('delete_domaine', id=domaine.id) }}"
                                                    data-delete-message="Êtes-vous sûr de vouloir supprimer le domaine '{{ domaine.nom }}'? Cette action supprimera également tous les thèmes associés à ce domaine."
                                                    data-use-modal="true"
                                                    data-delete-title="Confirmer la suppression">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Liste des Thèmes</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Domaine</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for theme in themes %}
                                <tr>
                                    <td>{{ theme.id }}</td>
                                    <td>{{ theme.nom }}</td>
                                    <td>{{ theme.domaine.nom }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_theme', id=theme.id) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                                    data-delete-url="{{ url_for('delete_theme', id=theme.id) }}"
                                                    data-delete-message="Êtes-vous sûr de vouloir supprimer le thème '{{ theme.nom }}'?"
                                                    data-use-modal="true"
                                                    data-delete-title="Confirmer la suppression">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='domaines_themes') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
