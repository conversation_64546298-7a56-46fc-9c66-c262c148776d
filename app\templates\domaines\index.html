{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-sitemap"></i> Domaines et Thèmes</h1>
        </div>
        <div class="col-md-4 text-right">
            <div class="btn-group" role="group">
                <a href="{{ url_for('new_domaine') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Ajouter un Domaine
                </a>
                <a href="{{ url_for('new_theme') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> Ajouter un Thème
                </a>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_domaines_themes') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un domaine ou un thème..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Liste des Domaines</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for domaine in domaines %}
                                <tr>
                                    <td>{{ domaine.id }}</td>
                                    <td>{{ domaine.nom }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_domaine', id=domaine.id) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteDomaineModal{{ domaine.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal pour confirmer la suppression -->
                                        <div class="modal fade" id="deleteDomaineModal{{ domaine.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteDomaineModalLabel{{ domaine.id }}" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteDomaineModalLabel{{ domaine.id }}">Confirmer la suppression</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Êtes-vous sûr de vouloir supprimer le domaine <strong>{{ domaine.nom }}</strong> ?
                                                        <div class="alert alert-warning mt-3">
                                                            <i class="fas fa-exclamation-triangle"></i> Attention: Cette action supprimera également tous les thèmes associés à ce domaine.
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                                                        <a href="{{ url_for('delete_domaine', id=domaine.id) }}" class="btn btn-danger">Supprimer</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Liste des Thèmes</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Domaine</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for theme in themes %}
                                <tr>
                                    <td>{{ theme.id }}</td>
                                    <td>{{ theme.nom }}</td>
                                    <td>{{ theme.domaine.nom }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_theme', id=theme.id) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteThemeModal{{ theme.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal pour confirmer la suppression -->
                                        <div class="modal fade" id="deleteThemeModal{{ theme.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteThemeModalLabel{{ theme.id }}" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteThemeModalLabel{{ theme.id }}">Confirmer la suppression</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Êtes-vous sûr de vouloir supprimer le thème <strong>{{ theme.nom }}</strong> ?
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                                                        <a href="{{ url_for('delete_theme', id=theme.id) }}" class="btn btn-danger">Supprimer</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='domaines_themes') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
