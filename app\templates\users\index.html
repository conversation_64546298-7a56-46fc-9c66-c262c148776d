{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-users me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Utilisateurs</span>
                        </h4>
                        <a href="{{ url_for('new_user') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nouvel Utilisateur
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_users') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un utilisateur..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Utilisateurs</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom d'utilisateur</th>
                            <th>Email</th>
                            <th>Nom complet</th>
                            <th>Admin</th>
                            <th>Permissions</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.nom_complet }}</td>
                            <td>
                                {% if user.is_admin %}
                                <span class="badge badge-success">Oui</span>
                                {% else %}
                                <span class="badge badge-secondary">Non</span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#permissionsModal{{ user.id }}">
                                    <i class="fas fa-key"></i> Voir
                                </button>

                                <!-- Modal pour afficher les permissions -->
                                <div class="modal fade" id="permissionsModal{{ user.id }}" tabindex="-1" role="dialog" aria-labelledby="permissionsModalLabel{{ user.id }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="permissionsModalLabel{{ user.id }}">Permissions de {{ user.username }}</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <ul class="list-group">
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        Fiche d'inscription
                                                        {% if user.perm_fiche_inscription %}
                                                        <span class="badge badge-success badge-pill"><i class="fas fa-check"></i></span>
                                                        {% else %}
                                                        <span class="badge badge-danger badge-pill"><i class="fas fa-times"></i></span>
                                                        {% endif %}
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        Dossier technique
                                                        {% if user.perm_dossier_technique %}
                                                        <span class="badge badge-success badge-pill"><i class="fas fa-check"></i></span>
                                                        {% else %}
                                                        <span class="badge badge-danger badge-pill"><i class="fas fa-times"></i></span>
                                                        {% endif %}
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        Dossier de remboursement
                                                        {% if user.perm_dossier_remboursement %}
                                                        <span class="badge badge-success badge-pill"><i class="fas fa-check"></i></span>
                                                        {% else %}
                                                        <span class="badge badge-danger badge-pill"><i class="fas fa-times"></i></span>
                                                        {% endif %}
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        Organisme
                                                        {% if user.perm_organisme %}
                                                        <span class="badge badge-success badge-pill"><i class="fas fa-check"></i></span>
                                                        {% else %}
                                                        <span class="badge badge-danger badge-pill"><i class="fas fa-times"></i></span>
                                                        {% endif %}
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        Formateur
                                                        {% if user.perm_formateur %}
                                                        <span class="badge badge-success badge-pill"><i class="fas fa-check"></i></span>
                                                        {% else %}
                                                        <span class="badge badge-danger badge-pill"><i class="fas fa-times"></i></span>
                                                        {% endif %}
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        Agenda des formateurs
                                                        {% if user.perm_agenda %}
                                                        <span class="badge badge-success badge-pill"><i class="fas fa-check"></i></span>
                                                        {% else %}
                                                        <span class="badge badge-danger badge-pill"><i class="fas fa-times"></i></span>
                                                        {% endif %}
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        Domaines et Thèmes
                                                        {% if user.perm_domaine_theme %}
                                                        <span class="badge badge-success badge-pill"><i class="fas fa-check"></i></span>
                                                        {% else %}
                                                        <span class="badge badge-danger badge-pill"><i class="fas fa-times"></i></span>
                                                        {% endif %}
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Fermer</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_user', id=user.id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> Modifier
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                            data-delete-url="{{ url_for('delete_user', id=user.id) }}"
                                            data-delete-message="Êtes-vous sûr de vouloir supprimer l'utilisateur '{{ user.username }}'?"
                                            data-use-modal="true"
                                            data-delete-title="Confirmer la suppression">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='users') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
