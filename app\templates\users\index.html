{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-users me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Utilisateurs</span>
                        </h4>
                        <a href="{{ url_for('new_user') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nouvel Utilisateur
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_users') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un utilisateur..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Utilisateurs</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom d'utilisateur</th>
                            <th>Email</th>
                            <th>Nom complet</th>
                            <th>Admin</th>
                            <th>Permissions</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.nom_complet }}</td>
                            <td>
                                {% if user.is_admin %}
                                <span class="badge badge-success">Oui</span>
                                {% else %}
                                <span class="badge badge-secondary">Non</span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#permissionsModal{{ user.id }}">
                                    <i class="fas fa-key"></i> Voir
                                </button>

                                <!-- Modal pour afficher les permissions -->
                                <div class="modal fade" id="permissionsModal{{ user.id }}" tabindex="-1" role="dialog" aria-labelledby="permissionsModalLabel{{ user.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                                <h5 class="modal-title font-weight-bold" id="permissionsModalLabel{{ user.id }}">
                                                    <i class="fas fa-shield-alt mr-2"></i>
                                                    Permissions de {{ user.username }}
                                                </h5>
                                                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body p-4">
                                                <!-- Groupe 1: Gestion des Dossiers -->
                                                <div class="permission-group-modal mb-4">
                                                    <h6 class="permission-group-title-modal">
                                                        <i class="fas fa-folder-open text-primary mr-2"></i>
                                                        Gestion des Dossiers
                                                    </h6>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="permission-item-modal">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <div class="permission-title-modal">Fiche d'inscription</div>
                                                                        <div class="permission-desc-modal">Créer, modifier et consulter</div>
                                                                    </div>
                                                                    {% if user.perm_fiche_inscription %}
                                                                    <span class="badge badge-success badge-pill"><i class="fas fa-check"></i> Autorisé</span>
                                                                    {% else %}
                                                                    <span class="badge badge-secondary badge-pill"><i class="fas fa-times"></i> Non autorisé</span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="permission-item-modal">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <div class="permission-title-modal">Dossier technique</div>
                                                                        <div class="permission-desc-modal">Gérer les dossiers techniques</div>
                                                                    </div>
                                                                    {% if user.perm_dossier_technique %}
                                                                    <span class="badge badge-success badge-pill"><i class="fas fa-check"></i> Autorisé</span>
                                                                    {% else %}
                                                                    <span class="badge badge-secondary badge-pill"><i class="fas fa-times"></i> Non autorisé</span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="permission-item-modal">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <div class="permission-title-modal">Dossier de remboursement</div>
                                                                        <div class="permission-desc-modal">Traiter les demandes</div>
                                                                    </div>
                                                                    {% if user.perm_dossier_remboursement %}
                                                                    <span class="badge badge-success badge-pill"><i class="fas fa-check"></i> Autorisé</span>
                                                                    {% else %}
                                                                    <span class="badge badge-secondary badge-pill"><i class="fas fa-times"></i> Non autorisé</span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Groupe 2: Gestion des Ressources -->
                                                <div class="permission-group-modal mb-4">
                                                    <h6 class="permission-group-title-modal">
                                                        <i class="fas fa-users text-success mr-2"></i>
                                                        Gestion des Ressources
                                                    </h6>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="permission-item-modal">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <div class="permission-title-modal">Organismes</div>
                                                                        <div class="permission-desc-modal">Gérer les organismes</div>
                                                                    </div>
                                                                    {% if user.perm_organisme %}
                                                                    <span class="badge badge-success badge-pill"><i class="fas fa-check"></i> Autorisé</span>
                                                                    {% else %}
                                                                    <span class="badge badge-secondary badge-pill"><i class="fas fa-times"></i> Non autorisé</span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="permission-item-modal">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <div class="permission-title-modal">Formateurs</div>
                                                                        <div class="permission-desc-modal">Gérer les profils</div>
                                                                    </div>
                                                                    {% if user.perm_formateur %}
                                                                    <span class="badge badge-success badge-pill"><i class="fas fa-check"></i> Autorisé</span>
                                                                    {% else %}
                                                                    <span class="badge badge-secondary badge-pill"><i class="fas fa-times"></i> Non autorisé</span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="permission-item-modal">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <div class="permission-title-modal">Agenda des formateurs</div>
                                                                        <div class="permission-desc-modal">Planifier et gérer</div>
                                                                    </div>
                                                                    {% if user.perm_agenda %}
                                                                    <span class="badge badge-success badge-pill"><i class="fas fa-check"></i> Autorisé</span>
                                                                    {% else %}
                                                                    <span class="badge badge-secondary badge-pill"><i class="fas fa-times"></i> Non autorisé</span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Groupe 3: Configuration -->
                                                <div class="permission-group-modal">
                                                    <h6 class="permission-group-title-modal">
                                                        <i class="fas fa-cogs text-warning mr-2"></i>
                                                        Configuration
                                                    </h6>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="permission-item-modal">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <div>
                                                                        <div class="permission-title-modal">Domaines et Thèmes</div>
                                                                        <div class="permission-desc-modal">Configurer les domaines</div>
                                                                    </div>
                                                                    {% if user.perm_domaine_theme %}
                                                                    <span class="badge badge-success badge-pill"><i class="fas fa-check"></i> Autorisé</span>
                                                                    {% else %}
                                                                    <span class="badge badge-secondary badge-pill"><i class="fas fa-times"></i> Non autorisé</span>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                                    <i class="fas fa-times mr-1"></i>Fermer
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_user', id=user.id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> Modifier
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" title="Supprimer"
                                            data-delete-url="{{ url_for('delete_user', id=user.id) }}"
                                            data-delete-message="Êtes-vous sûr de vouloir supprimer l'utilisateur '{{ user.username }}'?"
                                            data-use-modal="true"
                                            data-delete-title="Confirmer la suppression">
                                        <i class="fas fa-trash"></i> Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='users') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .permission-group-modal {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        border-left: 4px solid #007bff;
    }

    .permission-group-title-modal {
        font-weight: 600;
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.1rem;
    }

    .permission-item-modal {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .permission-item-modal:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-color: #007bff;
    }

    .permission-title-modal {
        font-weight: 600;
        color: #495057;
        font-size: 0.95rem;
    }

    .permission-desc-modal {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 2px;
    }

    .modal-content {
        border: none;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .modal-header {
        border-bottom: none;
    }

    .modal-footer {
        border-top: 1px solid #e9ecef;
        background: #f8f9fa;
    }

    .badge-pill {
        font-size: 0.75rem;
        padding: 0.5em 0.75em;
    }
</style>
{% endblock %}
