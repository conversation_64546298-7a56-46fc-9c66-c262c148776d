{% extends "base.html" %}

{% block title %}Sauve<PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-download"></i> Créer une Sauvegarde Manuelle
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> سيتم إنشاء نسخة احتياطية كاملة من قاعدة البيانات والملفات المرفقة.
                        قد تستغرق هذه العملية بعض الوقت حسب حجم البيانات.
                    </div>

                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    {{ form.nom_sauvegarde.label(class="form-label") }}
                                    {{ form.nom_sauvegarde(class="form-control") }}
                                    {% if form.nom_sauvegarde.errors %}
                                        <div class="text-danger">
                                            {% for error in form.nom_sauvegarde.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        اسم مميز للنسخة الاحتياطية (اختياري)
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.inclure_fichiers(class="form-check-input") }}
                                        {{ form.inclure_fichiers.label(class="form-check-label") }}
                                    </div>
                                    {% if form.inclure_fichiers.errors %}
                                        <div class="text-danger">
                                            {% for error in form.inclure_fichiers.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    {{ form.description.label(class="form-label") }}
                                    {{ form.description(class="form-control", rows="3") }}
                                    {% if form.description.errors %}
                                        <div class="text-danger">
                                            {% for error in form.description.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        وصف مختصر للنسخة الاحتياطية (اختياري)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle text-info"></i> معلومات النسخة الاحتياطية
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success"></i> قاعدة البيانات الكاملة</li>
                                    <li><i class="fas fa-check text-success"></i> إعدادات النظام</li>
                                    <li><i class="fas fa-check text-success"></i> الملفات المرفقة (إذا تم تحديدها)</li>
                                    <li><i class="fas fa-check text-success"></i> ضغط الملفات لتوفير المساحة</li>
                                </ul>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-success btn-lg" id="backupBtn">
                                <i class="fas fa-download"></i> إنشاء النسخة الاحتياطية
                            </button>
                            <a href="{{ url_for('backup_list') }}" class="btn btn-info">
                                <i class="fas fa-list"></i> عرض النسخ الاحتياطية
                            </a>
                            <a href="{{ url_for('backup_settings') }}" class="btn btn-warning">
                                <i class="fas fa-cog"></i> الإعدادات
                            </a>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('backupBtn').addEventListener('click', function() {
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء النسخة الاحتياطية...';
    this.disabled = true;
});
</script>
{% endblock %}
