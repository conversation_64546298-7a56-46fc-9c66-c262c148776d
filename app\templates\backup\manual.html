{% extends "base.html" %}

{% block title %}Sauvegarde Manuelle{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-download me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Sauvegarde Manuelle</span>
                        </h4>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Attention:</strong> Une sauvegarde complète de la base de données et des fichiers joints sera créée.
                        Cette opération peut prendre du temps selon la taille des données.
                    </div>

                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group mb-3">
                                    {{ form.nom_fichier.label(class="form-label") }}
                                    {{ form.nom_fichier(class="form-control") }}
                                    {% if form.nom_fichier.errors %}
                                        <div class="text-danger">
                                            {% for error in form.nom_fichier.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Nom distinctif pour la sauvegarde (optionnel)
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.inclure_fichiers(class="form-check-input") }}
                                        {{ form.inclure_fichiers.label(class="form-check-label") }}
                                    </div>
                                    {% if form.inclure_fichiers.errors %}
                                        <div class="text-danger">
                                            {% for error in form.inclure_fichiers.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    {{ form.description.label(class="form-label") }}
                                    {{ form.description(class="form-control", rows="3") }}
                                    {% if form.description.errors %}
                                        <div class="text-danger">
                                            {% for error in form.description.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Description courte de la sauvegarde (optionnel)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle text-info"></i> Informations de la sauvegarde
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success"></i> Base de données complète</li>
                                    <li><i class="fas fa-check text-success"></i> Paramètres du système</li>
                                    <li><i class="fas fa-check text-success"></i> Fichiers joints (si sélectionnés)</li>
                                    <li><i class="fas fa-check text-success"></i> Compression des fichiers pour économiser l'espace</li>
                                </ul>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-success btn-lg" id="backupBtn">
                                <i class="fas fa-download"></i> Créer la sauvegarde
                            </button>
                            <a href="{{ url_for('backup_list') }}" class="btn btn-info">
                                <i class="fas fa-list"></i> Voir les sauvegardes
                            </a>
                            <a href="{{ url_for('backup_settings') }}" class="btn btn-warning">
                                <i class="fas fa-cog"></i> Paramètres
                            </a>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('backupBtn').addEventListener('click', function() {
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Création de la sauvegarde en cours...';
    this.disabled = true;
});
</script>
{% endblock %}
