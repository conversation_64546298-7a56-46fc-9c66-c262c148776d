# Guide de création du fichier exécutable pour Gestion des Formations

Ce guide explique comment créer un fichier exécutable (.exe) et un installateur pour l'application Gestion des Formations.

## Prérequis

- Python 3.8 ou supérieur
- pip (gestionnaire de paquets Python)
- <PERSON><PERSON> (pour créer l'installateur)

## Étape 1: Installer les dépendances nécessaires

```bash
pip install pyinstaller
pip install -r requirements.txt
```

## Étape 2: Créer le fichier exécutable avec PyInstaller

Vous pouvez utiliser le fichier spec déjà préparé:

```bash
pyinstaller gestion_formation.spec
```

Ou exécuter la commande complète:

```bash
pyinstaller --noconfirm --onefile --windowed --add-data "app/templates;app/templates" --add-data "app/static;app/static" --add-data "app/static/uploads;app/static/uploads" --add-data "migrations;migrations" --add-data "app.db;." --hidden-import email_validator --hidden-import flask --hidden-import flask_sqlalchemy --hidden-import flask_migrate --hidden-import flask_login --hidden-import flask_wtf --hidden-import wtforms --hidden-import werkzeug --hidden-import jinja2 --hidden-import sqlalchemy --hidden-import email_validator --hidden-import itsdangerous --hidden-import click --hidden-import markupsafe --icon=app/static/img/favicon.ico run.py
```

Cette commande va créer un dossier `dist` contenant le fichier exécutable `Gestion_Formation.exe`.

## Étape 3: Créer l'installateur avec Inno Setup

1. Installez Inno Setup depuis [le site officiel](https://jrsoftware.org/isdl.php)
2. Ouvrez le fichier `gestion_formation_installer.iss` avec Inno Setup
3. Cliquez sur "Compiler" pour créer l'installateur

L'installateur sera créé dans le dossier `Output` et s'appellera `Gestion_Formation_Setup.exe`.

## Fonctionnalités de l'installateur

- Installation dans le dossier Programme Files
- Création d'un raccourci sur le bureau (optionnel)
- Ajout d'une règle de pare-feu pour autoriser le port 5000
- Suppression de la règle de pare-feu lors de la désinstallation

## Utilisation de l'application

Après l'installation:

1. Lancez l'application depuis le menu Démarrer ou le raccourci sur le bureau
2. Le navigateur s'ouvrira automatiquement à l'adresse de l'application
3. L'application sera accessible depuis n'importe quel appareil du réseau local à l'adresse http://IP_DE_VOTRE_PC:5000

## Remarques importantes

- L'application utilise le port 5000. Assurez-vous qu'il n'est pas utilisé par une autre application.
- La base de données SQLite est incluse dans l'exécutable. Toutes les données seront conservées entre les sessions.
- Pour mettre à jour l'application, il suffit de désinstaller l'ancienne version et d'installer la nouvelle.
