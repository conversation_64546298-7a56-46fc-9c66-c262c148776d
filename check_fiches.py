from app import create_app, db
from app.models import FicheInscription

def check_fiches():
    app = create_app()
    with app.app_context():
        fiches = FicheInscription.query.all()
        print('Fiches d\'Inscription:')
        for fiche in fiches:
            print(f'ID: {fiche.id}, Raison Sociale: {fiche.raison_sociale}, Email: {fiche.email}, Eligible: {fiche.eligible}')
            print(f'Validation: {fiche.validation}, Date Validation: {fiche.date_validation}, Dépôt Physique: {fiche.depot_physique}, Date Dépôt: {fiche.date_depot}')

if __name__ == '__main__':
    check_fiches()
