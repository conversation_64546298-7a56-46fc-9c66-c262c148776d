{% extends "base.html" %}

{% block title %}Informations Société{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-building"></i> Informations de la Société
                    </h4>
                </div>
                <div class="card-body">
                    {% if company and company.logo_path %}
                    <div class="text-center mb-4">
                        <img src="{{ url_for('static', filename=company.logo_path) }}" 
                             alt="Logo de l'entreprise"
                             class="img-fluid" 
                             style="max-height: 150px;">
                    </div>
                    {% endif %}

                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.nom_entreprise.label(class="form-label") }}
                                    {{ form.nom_entreprise(class="form-control") }}
                                    {% if form.nom_entreprise.errors %}
                                        <div class="text-danger">
                                            {% for error in form.nom_entreprise.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.slogan.label(class="form-label") }}
                                    {{ form.slogan(class="form-control") }}
                                    {% if form.slogan.errors %}
                                        <div class="text-danger">
                                            {% for error in form.slogan.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    {{ form.adresse.label(class="form-label") }}
                                    {{ form.adresse(class="form-control", rows="3") }}
                                    {% if form.adresse.errors %}
                                        <div class="text-danger">
                                            {% for error in form.adresse.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    {{ form.telephone.label(class="form-label") }}
                                    {{ form.telephone(class="form-control") }}
                                    {% if form.telephone.errors %}
                                        <div class="text-danger">
                                            {% for error in form.telephone.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    {{ form.fax.label(class="form-label") }}
                                    {{ form.fax(class="form-control") }}
                                    {% if form.fax.errors %}
                                        <div class="text-danger">
                                            {% for error in form.fax.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control") }}
                                    {% if form.email.errors %}
                                        <div class="text-danger">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.site_web.label(class="form-label") }}
                                    {{ form.site_web(class="form-control") }}
                                    {% if form.site_web.errors %}
                                        <div class="text-danger">
                                            {% for error in form.site_web.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.logo.label(class="form-label") }}
                                    {{ form.logo(class="form-control") }}
                                    {% if form.logo.errors %}
                                        <div class="text-danger">
                                            {% for error in form.logo.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Formats supportés : PNG, JPG, JPEG, GIF (maximum 5 Mo)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.couleur_principale.label(class="form-label") }}
                                    {{ form.couleur_principale(class="form-control", type="color") }}
                                    {% if form.couleur_principale.errors %}
                                        <div class="text-danger">
                                            {% for error in form.couleur_principale.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.couleur_secondaire.label(class="form-label") }}
                                    {{ form.couleur_secondaire(class="form-control", type="color") }}
                                    {% if form.couleur_secondaire.errors %}
                                        <div class="text-danger">
                                            {% for error in form.couleur_secondaire.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    {{ form.pied_de_page.label(class="form-label") }}
                                    {{ form.pied_de_page(class="form-control", rows="3") }}
                                    {% if form.pied_de_page.errors %}
                                        <div class="text-danger">
                                            {% for error in form.pied_de_page.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
