from flask import render_template, flash, redirect, url_for, request, send_from_directory, abort, jsonify
from sqlalchemy import or_
from flask_login import login_user, logout_user, current_user, login_required
from urllib.parse import urlsplit
from datetime import datetime, timezone
import os
from werkzeug.utils import secure_filename
from app import db
from app.forms import (
    LoginForm, FormationForm, UserForm, OrganismeForm, FormateurForm,
    AgendaFormateurForm, DomaineForm, ThemeForm, FicheInscriptionForm,
    DossierTechniqueForm, DossierRemboursementForm, RapportForm,
    CompanyInfoForm, BackupSettingsForm, ManualBackupForm, ImportDatabaseForm,
    ActivityLogSearchForm
)
from app.models import (
    User, Formation, Organisme, DossierTechnique, DossierRemboursement,
    Formateur, Domaine, Theme, FicheInscription, AgendaFormateur,
    Rapport, Notification, CompanyInfo, UserActivityLog, BackupSettings,
    BackupLog
)

@login_required
def index():
    return redirect(url_for('dashboard'))

def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user is None or not user.check_password(form.password.data):
            flash('Nom d\'utilisateur ou mot de passe invalide')
            return redirect(url_for('login'))
        login_user(user, remember=form.remember_me.data)
        next_page = request.args.get('next')
        if not next_page or urlsplit(next_page).netloc != '':
            next_page = url_for('index')
        return redirect(next_page)
    return render_template('login.html', title='Connexion', form=form)

def logout():
    logout_user()
    return redirect(url_for('index'))

# Configuration du dossier pour stocker les fichiers joints
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_file(file):
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Ajouter un horodatage au fichier pour éviter la duplication des noms
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        return filename
    return None

def check_permission(permission):
    """Vérification des permissions utilisateur"""
    if not current_user.is_authenticated:
        return False
    return current_user.has_permission(permission)

@login_required
def dashboard():
    # Statistiques pour le tableau de bord
    stats = {
        'formations': FicheInscription.query.count(),
        'dossiers_techniques': DossierTechnique.query.count(),
        'dossiers_remboursement': DossierRemboursement.query.count(),
        'organismes': Organisme.query.count(),
        'formateurs': Formateur.query.count(),
        'users': User.query.count()
    }

    # Dernières notifications
    notifications = Notification.query.order_by(Notification.date_creation.desc()).limit(5).all()

    # Dernières activités
    recent_formations = Formation.query.order_by(Formation.date.desc()).limit(3).all()
    recent_dossiers = DossierTechnique.query.order_by(DossierTechnique.id.desc()).limit(3).all()

    return render_template('dashboard.html',
                          title='Tableau de Bord',
                          stats=stats,
                          notifications=notifications,
                          recent_formations=recent_formations,
                          recent_dossiers=recent_dossiers)

@login_required
def formations():
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    # Statistiques pour le tableau de bord
    stats = {
        'formations': Formation.query.count(),
        'dossiers_techniques': DossierTechnique.query.count(),
        'dossiers_remboursement': DossierRemboursement.query.count(),
        'organismes': Organisme.query.count(),
        'formateurs': Formateur.query.count(),
        'users': User.query.count()
    }

    # Dernières notifications
    notifications = Notification.query.order_by(Notification.date_creation.desc()).limit(5).all()

    # Dernières activités
    recent_formations = Formation.query.order_by(Formation.date.desc()).limit(3).all()
    recent_dossiers = DossierTechnique.query.order_by(DossierTechnique.id.desc()).limit(3).all()

    return redirect(url_for('dashboard'))

@login_required
def dossiers_techniques():
    if not check_permission('dossier_technique'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    dossiers = DossierTechnique.query.all()
    return render_template('dossiers_techniques/index.html', title='Dossiers Techniques', dossiers=dossiers)

@login_required
def new_dossier_technique():
    if not check_permission('dossier_technique'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = DossierTechniqueForm()

    # Remplir les choix des listes déroulantes
    form.fiche_inscription.choices = [(f.id, f.raison_sociale) for f in FicheInscription.query.all()]
    form.domaine.choices = [(d.id, d.nom) for d in Domaine.query.all()]
    form.theme.choices = [(t.id, t.nom) for t in Theme.query.all()]
    form.organisme_formation.choices = [(o.id, o.raison_sociale) for o in Organisme.query.all()]

    if form.validate_on_submit():
        # Traitement du fichier joint
        piece_jointe = None
        if form.piece_jointe.data:
            piece_jointe = save_file(form.piece_jointe.data)

        dossier = DossierTechnique(
            fiche_inscription_id=form.fiche_inscription.data,
            domaine_id=form.domaine.data,
            theme_id=form.theme.data,
            objectif=form.objectif.data,
            contenu_indicatif=form.contenu_indicatif.data,
            organisme_formation_id=form.organisme_formation.data,
            num_cnss_organisme=form.num_cnss_organisme.data,
            type_formation=form.type_formation.data,
            cout_formation_ht=form.cout_formation_ht.data,
            effectif_global=form.effectif_global.data,
            nombre_cadres=form.nombre_cadres.data,
            nombre_employes=form.nombre_employes.data,
            nombre_ouvriers=form.nombre_ouvriers.data,
            conforme=form.conforme.data,
            depot_physique=form.depot_physique.data,
            date_depot=form.date_depot.data,
            validation=form.validation.data,
            piece_jointe=piece_jointe
        )
        db.session.add(dossier)
        db.session.commit()
        flash('Le dossier technique a été créé avec succès!', 'success')
        return redirect(url_for('dossiers_techniques'))

    return render_template('dossiers_techniques/new.html', title='Nouveau Dossier Technique', form=form)

@login_required
def edit_dossier_technique(id):
    if not check_permission('dossier_technique'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    dossier = DossierTechnique.query.get_or_404(id)
    form = DossierTechniqueForm()

    # Remplir les choix des listes déroulantes
    form.fiche_inscription.choices = [(f.id, f.raison_sociale) for f in FicheInscription.query.all()]
    form.domaine.choices = [(d.id, d.nom) for d in Domaine.query.all()]
    form.theme.choices = [(t.id, t.nom) for t in Theme.query.all()]
    form.organisme_formation.choices = [(o.id, o.raison_sociale) for o in Organisme.query.all()]

    if form.validate_on_submit():
        dossier.fiche_inscription_id = form.fiche_inscription.data
        dossier.domaine_id = form.domaine.data
        dossier.theme_id = form.theme.data
        dossier.objectif = form.objectif.data
        dossier.contenu_indicatif = form.contenu_indicatif.data
        dossier.organisme_formation_id = form.organisme_formation.data
        dossier.num_cnss_organisme = form.num_cnss_organisme.data
        dossier.type_formation = form.type_formation.data
        dossier.cout_formation_ht = form.cout_formation_ht.data
        dossier.effectif_global = form.effectif_global.data
        dossier.nombre_cadres = form.nombre_cadres.data
        dossier.nombre_employes = form.nombre_employes.data
        dossier.nombre_ouvriers = form.nombre_ouvriers.data
        dossier.conforme = form.conforme.data
        dossier.depot_physique = form.depot_physique.data
        dossier.date_depot = form.date_depot.data
        dossier.validation = form.validation.data

        # Traitement du fichier joint
        if form.piece_jointe.data:
            dossier.piece_jointe = save_file(form.piece_jointe.data)

        db.session.commit()
        flash('Le dossier technique a été modifié avec succès!', 'success')
        return redirect(url_for('dossiers_techniques'))

    if request.method == 'GET':
        form.fiche_inscription.data = dossier.fiche_inscription_id
        form.domaine.data = dossier.domaine_id
        form.theme.data = dossier.theme_id
        form.objectif.data = dossier.objectif
        form.contenu_indicatif.data = dossier.contenu_indicatif
        form.organisme_formation.data = dossier.organisme_formation_id
        form.num_cnss_organisme.data = dossier.num_cnss_organisme
        form.type_formation.data = dossier.type_formation
        form.cout_formation_ht.data = dossier.cout_formation_ht
        form.effectif_global.data = dossier.effectif_global
        form.nombre_cadres.data = dossier.nombre_cadres
        form.nombre_employes.data = dossier.nombre_employes
        form.nombre_ouvriers.data = dossier.nombre_ouvriers
        form.conforme.data = dossier.conforme
        form.depot_physique.data = dossier.depot_physique
        form.date_depot.data = dossier.date_depot
        form.validation.data = dossier.validation

    return render_template('dossiers_techniques/edit.html', title='Modifier Dossier Technique', form=form, dossier=dossier)

@login_required
def delete_dossier_technique(id):
    if not check_permission('dossier_technique'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    dossier = DossierTechnique.query.get_or_404(id)

    # Pas besoin de vérifier si le dossier technique est utilisé dans des dossiers de remboursement
    # car il n'y a pas de relation directe entre les deux

    db.session.delete(dossier)
    db.session.commit()
    log_user_activity('delete', 'dossier_technique', f'Suppression du dossier technique: {dossier.nom}')
    flash('Le dossier technique a été supprimé avec succès!', 'success')
    return redirect(url_for('dossiers_techniques'))

@login_required
def remboursements():
    if not check_permission('dossier_remboursement'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    remboursements = DossierRemboursement.query.all()
    return render_template('remboursements/index.html', title='Remboursements', remboursements=remboursements)

@login_required
def new_remboursement():
    if not check_permission('dossier_remboursement'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = DossierRemboursementForm()

    # Remplir les choix des listes déroulantes
    form.organisme.choices = [(o.id, o.raison_sociale) for o in Organisme.query.all()]
    form.formateur.choices = [(f.id, f.nom_prenom) for f in Formateur.query.all()]
    form.fiche_inscription.choices = [(f.id, f.raison_sociale) for f in FicheInscription.query.all()]

    if form.validate_on_submit():
        # Traitement du fichier joint
        piece_jointe = None
        if form.piece_jointe.data:
            piece_jointe = save_file(form.piece_jointe.data)

        remboursement = DossierRemboursement(
            organisme_id=form.organisme.data,
            formateur_id=form.formateur.data,
            fiche_inscription_id=form.fiche_inscription.data,
            theme=form.theme.data,
            date=form.date.data,
            contrat=form.contrat.data,
            f2=form.f2.data,
            liste_de_presence=form.liste_de_presence.data,
            fiche_synthetique_evaluation_formateur=form.fiche_synthetique_evaluation_formateur.data,
            f4=form.f4.data,
            facturation=form.facturation.data,
            mode_de_reglement=form.mode_de_reglement.data,
            m6=form.m6.data,
            piece_jointe=piece_jointe
        )
        db.session.add(remboursement)
        db.session.commit()
        flash('Le dossier de remboursement a été créé avec succès!', 'success')
        return redirect(url_for('remboursements'))

    return render_template('remboursements/new.html', title='Nouveau Dossier de Remboursement', form=form)

@login_required
def edit_remboursement(id):
    if not check_permission('dossier_remboursement'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    remboursement = DossierRemboursement.query.get_or_404(id)
    form = DossierRemboursementForm()

    # Remplir les choix des listes déroulantes
    form.organisme.choices = [(o.id, o.raison_sociale) for o in Organisme.query.all()]
    form.formateur.choices = [(f.id, f.nom_prenom) for f in Formateur.query.all()]
    form.fiche_inscription.choices = [(f.id, f.raison_sociale) for f in FicheInscription.query.all()]

    if form.validate_on_submit():
        remboursement.organisme_id = form.organisme.data
        remboursement.formateur_id = form.formateur.data
        remboursement.fiche_inscription_id = form.fiche_inscription.data
        remboursement.theme = form.theme.data
        remboursement.date = form.date.data
        remboursement.contrat = form.contrat.data
        remboursement.f2 = form.f2.data
        remboursement.liste_de_presence = form.liste_de_presence.data
        remboursement.fiche_synthetique_evaluation_formateur = form.fiche_synthetique_evaluation_formateur.data
        remboursement.f4 = form.f4.data
        remboursement.facturation = form.facturation.data
        remboursement.mode_de_reglement = form.mode_de_reglement.data
        remboursement.m6 = form.m6.data

        # Traitement du fichier joint
        if form.piece_jointe.data:
            remboursement.piece_jointe = save_file(form.piece_jointe.data)

        db.session.commit()
        flash('Le dossier de remboursement a été modifié avec succès!', 'success')
        return redirect(url_for('remboursements'))

    if request.method == 'GET':
        form.organisme.data = remboursement.organisme_id
        form.formateur.data = remboursement.formateur_id
        form.fiche_inscription.data = remboursement.fiche_inscription_id
        form.theme.data = remboursement.theme
        form.date.data = remboursement.date
        form.contrat.data = remboursement.contrat
        form.f2.data = remboursement.f2
        form.liste_de_presence.data = remboursement.liste_de_presence
        form.fiche_synthetique_evaluation_formateur.data = remboursement.fiche_synthetique_evaluation_formateur
        form.f4.data = remboursement.f4
        form.facturation.data = remboursement.facturation
        form.mode_de_reglement.data = remboursement.mode_de_reglement
        form.m6.data = remboursement.m6

    return render_template('remboursements/edit.html', title='Modifier Dossier de Remboursement', form=form, remboursement=remboursement)

@login_required
def delete_remboursement(id):
    if not check_permission('dossier_remboursement'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    remboursement = DossierRemboursement.query.get_or_404(id)
    db.session.delete(remboursement)
    db.session.commit()
    flash('Le dossier de remboursement a été supprimé avec succès!', 'success')
    return redirect(url_for('remboursements'))

# Routes pour les fiches d'inscription
@login_required
def fiches_inscription():
    if not check_permission('fiche_inscription'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    fiches = FicheInscription.query.all()
    return render_template('fiches_inscription/index.html', title='Fiches d\'Inscription', fiches=fiches)

@login_required
def new_fiche_inscription():
    if not check_permission('fiche_inscription'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = FicheInscriptionForm()

    if form.validate_on_submit():
        # Traitement du fichier joint
        piece_jointe = None
        if form.piece_jointe.data:
            piece_jointe = save_file(form.piece_jointe.data)

        fiche = FicheInscription(
            date_inscription=form.date_inscription.data,
            raison_sociale=form.raison_sociale.data,
            tel_entreprise=form.tel_entreprise.data,
            fax_entreprise=form.fax_entreprise.data,
            email=form.email.data,
            mot_de_passe_email=form.mot_de_passe_email.data,
            patente=form.patente.data,
            identifiant_fiscale=form.identifiant_fiscale.data,
            num_rc=form.num_rc.data,
            num_cnss=form.num_cnss.data,
            eligible=form.eligible.data,
            ice=form.ice.data,
            mot_de_passe_ice=form.mot_de_passe_ice.data,
            nombre_cadres=form.nombre_cadres.data,
            nombre_employes=form.nombre_employes.data,
            nombre_ouvriers=form.nombre_ouvriers.data,
            validation=form.validation.data,
            date_validation=form.date_validation.data,
            depot_physique=form.depot_physique.data,
            date_depot=form.date_depot.data,
            piece_jointe=piece_jointe
        )
        db.session.add(fiche)
        db.session.commit()
        flash('La fiche d\'inscription a été créée avec succès!', 'success')
        return redirect(url_for('fiches_inscription'))

    return render_template('fiches_inscription/new.html', title='Nouvelle Fiche d\'Inscription', form=form)

@login_required
def edit_fiche_inscription(id):
    if not check_permission('fiche_inscription'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    fiche = FicheInscription.query.get_or_404(id)
    form = FicheInscriptionForm()

    if form.validate_on_submit():
        fiche.date_inscription = form.date_inscription.data
        fiche.raison_sociale = form.raison_sociale.data
        fiche.tel_entreprise = form.tel_entreprise.data
        fiche.fax_entreprise = form.fax_entreprise.data
        fiche.email = form.email.data
        fiche.mot_de_passe_email = form.mot_de_passe_email.data
        fiche.patente = form.patente.data
        fiche.identifiant_fiscale = form.identifiant_fiscale.data
        fiche.num_rc = form.num_rc.data
        fiche.num_cnss = form.num_cnss.data
        fiche.eligible = form.eligible.data
        fiche.ice = form.ice.data
        fiche.mot_de_passe_ice = form.mot_de_passe_ice.data
        fiche.nombre_cadres = form.nombre_cadres.data
        fiche.nombre_employes = form.nombre_employes.data
        fiche.nombre_ouvriers = form.nombre_ouvriers.data
        fiche.validation = form.validation.data
        fiche.date_validation = form.date_validation.data
        fiche.depot_physique = form.depot_physique.data
        fiche.date_depot = form.date_depot.data

        # Traitement du fichier joint
        if form.piece_jointe.data:
            fiche.piece_jointe = save_file(form.piece_jointe.data)

        db.session.commit()
        flash('La fiche d\'inscription a été modifiée avec succès!', 'success')
        return redirect(url_for('fiches_inscription'))

    if request.method == 'GET':
        form.date_inscription.data = fiche.date_inscription
        form.raison_sociale.data = fiche.raison_sociale
        form.tel_entreprise.data = fiche.tel_entreprise
        form.fax_entreprise.data = fiche.fax_entreprise
        form.email.data = fiche.email
        form.mot_de_passe_email.data = fiche.mot_de_passe_email
        form.patente.data = fiche.patente
        form.identifiant_fiscale.data = fiche.identifiant_fiscale
        form.num_rc.data = fiche.num_rc
        form.num_cnss.data = fiche.num_cnss
        form.eligible.data = fiche.eligible
        form.ice.data = fiche.ice
        form.mot_de_passe_ice.data = fiche.mot_de_passe_ice
        form.nombre_cadres.data = fiche.nombre_cadres
        form.nombre_employes.data = fiche.nombre_employes
        form.nombre_ouvriers.data = fiche.nombre_ouvriers
        form.validation.data = fiche.validation
        form.date_validation.data = fiche.date_validation
        form.depot_physique.data = fiche.depot_physique
        form.date_depot.data = fiche.date_depot

    return render_template('fiches_inscription/edit.html', title='Modifier Fiche d\'Inscription', form=form, fiche=fiche)

@login_required
def delete_fiche_inscription(id):
    if not check_permission('fiche_inscription'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    fiche = FicheInscription.query.get_or_404(id)

    # Vérifier si la fiche est utilisée dans des dossiers techniques
    if DossierTechnique.query.filter_by(fiche_inscription_id=fiche.id).count() > 0:
        flash('Cette fiche d\'inscription ne peut pas être supprimée car elle est utilisée dans des dossiers techniques.', 'danger')
        return redirect(url_for('fiches_inscription'))

    # Vérifier si la fiche est utilisée dans des dossiers de remboursement
    if DossierRemboursement.query.filter_by(fiche_inscription_id=fiche.id).count() > 0:
        flash('Cette fiche d\'inscription ne peut pas être supprimée car elle est utilisée dans des dossiers de remboursement.', 'danger')
        return redirect(url_for('fiches_inscription'))

    db.session.delete(fiche)
    db.session.commit()
    flash('La fiche d\'inscription a été supprimée avec succès!', 'success')
    return redirect(url_for('fiches_inscription'))

# Route pour l'éligibilité OFPPT
@login_required
def eligibilite_ofppt():
    if not check_permission('fiche_inscription'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    return render_template('eligibilite_ofppt.html', title='Éligibilité OFPPT')

# Routes pour les utilisateurs
@login_required
def users():
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    users = User.query.all()
    return render_template('users/index.html', title='Utilisateurs', users=users)

@login_required
def new_user():
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = UserForm()

    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            nom_complet=form.nom_complet.data,
            is_admin=form.is_admin.data,
            perm_fiche_inscription=bool(form.perm_fiche_inscription.data),
            perm_dossier_technique=bool(form.perm_dossier_technique.data),
            perm_dossier_remboursement=bool(form.perm_dossier_remboursement.data),
            perm_organisme=bool(form.perm_organisme.data),
            perm_formateur=bool(form.perm_formateur.data),
            perm_agenda=bool(form.perm_agenda.data),
            perm_domaine_theme=bool(form.perm_domaine_theme.data)
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        flash('L\'utilisateur a été créé avec succès!', 'success')
        return redirect(url_for('users'))

    return render_template('users/new.html', title='Nouvel Utilisateur', form=form)

@login_required
def edit_user(id):
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    user = User.query.get_or_404(id)
    form = UserForm()
    form.user_id = user.id  # Pour la validation

    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.nom_complet = form.nom_complet.data
        user.is_admin = form.is_admin.data
        user.perm_fiche_inscription = bool(form.perm_fiche_inscription.data)
        user.perm_dossier_technique = bool(form.perm_dossier_technique.data)
        user.perm_dossier_remboursement = bool(form.perm_dossier_remboursement.data)
        user.perm_organisme = bool(form.perm_organisme.data)
        user.perm_formateur = bool(form.perm_formateur.data)
        user.perm_agenda = bool(form.perm_agenda.data)
        user.perm_domaine_theme = bool(form.perm_domaine_theme.data)

        if form.password.data:
            user.set_password(form.password.data)

        db.session.commit()
        flash('L\'utilisateur a été modifié avec succès!', 'success')
        return redirect(url_for('users'))

    if request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.nom_complet.data = user.nom_complet
        form.is_admin.data = user.is_admin
        form.perm_fiche_inscription.data = user.perm_fiche_inscription
        form.perm_dossier_technique.data = user.perm_dossier_technique
        form.perm_dossier_remboursement.data = user.perm_dossier_remboursement
        form.perm_organisme.data = user.perm_organisme
        form.perm_formateur.data = user.perm_formateur
        form.perm_agenda.data = user.perm_agenda
        form.perm_domaine_theme.data = user.perm_domaine_theme

    return render_template('users/edit.html', title='Modifier Utilisateur', form=form, user=user)

@login_required
def delete_user(id):
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    user = User.query.get_or_404(id)

    # Ne pas supprimer l'utilisateur connecté
    if user.id == current_user.id:
        flash('Vous ne pouvez pas supprimer votre propre compte.', 'danger')
        return redirect(url_for('users'))

    db.session.delete(user)
    db.session.commit()
    log_user_activity('delete', 'user', f'Suppression de l\'utilisateur: {user.username}')
    flash('L\'utilisateur a été supprimé avec succès!', 'success')
    return redirect(url_for('users'))

# Routes pour les domaines et thèmes
@login_required
def domaines_themes():
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    domaines = Domaine.query.all()
    themes = Theme.query.all()
    return render_template('domaines/index.html', title='Domaines et Thèmes', domaines=domaines, themes=themes)

@login_required
def new_domaine():
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = DomaineForm()

    if form.validate_on_submit():
        domaine = Domaine(nom=form.nom.data)
        db.session.add(domaine)
        db.session.commit()
        flash('Le domaine a été créé avec succès!', 'success')
        return redirect(url_for('domaines_themes'))

    return render_template('domaines/new_domaine.html', title='Nouveau Domaine', form=form)

@login_required
def edit_domaine(id):
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    domaine = Domaine.query.get_or_404(id)
    form = DomaineForm()

    if form.validate_on_submit():
        domaine.nom = form.nom.data
        db.session.commit()
        flash('Le domaine a été modifié avec succès!', 'success')
        return redirect(url_for('domaines_themes'))

    if request.method == 'GET':
        form.nom.data = domaine.nom

    return render_template('domaines/edit_domaine.html', title='Modifier Domaine', form=form, domaine=domaine)

@login_required
def delete_domaine(id):
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    domaine = Domaine.query.get_or_404(id)

    # Vérifier si le domaine est utilisé dans des thèmes
    if Theme.query.filter_by(domaine_id=domaine.id).count() > 0:
        flash('Ce domaine ne peut pas être supprimé car il est utilisé dans des thèmes.', 'danger')
        return redirect(url_for('domaines_themes'))

    db.session.delete(domaine)
    db.session.commit()
    flash('Le domaine a été supprimé avec succès!', 'success')
    return redirect(url_for('domaines_themes'))

@login_required
def new_theme():
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = ThemeForm()
    form.domaine.choices = [(d.id, d.nom) for d in Domaine.query.all()]

    if form.validate_on_submit():
        theme = Theme(nom=form.nom.data, domaine_id=form.domaine.data)
        db.session.add(theme)
        db.session.commit()
        flash('Le thème a été créé avec succès!', 'success')
        return redirect(url_for('domaines_themes'))

    return render_template('domaines/new_theme.html', title='Nouveau Thème', form=form)

@login_required
def edit_theme(id):
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    theme = Theme.query.get_or_404(id)
    form = ThemeForm()
    form.domaine.choices = [(d.id, d.nom) for d in Domaine.query.all()]

    if form.validate_on_submit():
        theme.nom = form.nom.data
        theme.domaine_id = form.domaine.data
        db.session.commit()
        flash('Le thème a été modifié avec succès!', 'success')
        return redirect(url_for('domaines_themes'))

    if request.method == 'GET':
        form.nom.data = theme.nom
        form.domaine.data = theme.domaine_id

    return render_template('domaines/edit_theme.html', title='Modifier Thème', form=form, theme=theme)

@login_required
def delete_theme(id):
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    theme = Theme.query.get_or_404(id)

    # Vérifier si le thème est utilisé dans des dossiers techniques
    if DossierTechnique.query.filter_by(theme_id=theme.id).count() > 0:
        flash('Ce thème ne peut pas être supprimé car il est utilisé dans des dossiers techniques.', 'danger')
        return redirect(url_for('domaines_themes'))

    db.session.delete(theme)
    db.session.commit()
    flash('Le thème a été supprimé avec succès!', 'success')
    return redirect(url_for('domaines_themes'))

# Routes pour l'agenda des formateurs
@login_required
def agenda_formateurs():
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    import calendar
    from datetime import datetime, date

    # Récupérer le mois et l'année à afficher
    current_date = datetime.now()
    current_month = int(request.args.get('month', current_date.month))
    current_year = int(request.args.get('year', current_date.year))

    # Créer un calendrier pour le mois en cours
    cal = calendar.monthcalendar(current_year, current_month)

    # Récupérer tous les rendez-vous pour le mois en cours
    start_date = date(current_year, current_month, 1)
    if current_month == 12:
        end_date = date(current_year + 1, 1, 1)
    else:
        end_date = date(current_year, current_month + 1, 1)

    agendas = AgendaFormateur.query.filter(
        AgendaFormateur.date_rendezvous >= start_date,
        AgendaFormateur.date_rendezvous < end_date
    ).all()

    # Organiser les rendez-vous par jour
    events_by_day = {}
    for agenda in agendas:
        day = agenda.date_rendezvous.day
        if day not in events_by_day:
            events_by_day[day] = []
        events_by_day[day].append(agenda)

    # Ajouter les événements au calendrier
    calendar_with_events = []
    for week in cal:
        week_with_events = []
        for day in week:
            if day == 0:
                week_with_events.append((0, []))
            else:
                week_with_events.append((day, events_by_day.get(day, [])))
        calendar_with_events.append(week_with_events)

    # Nom du mois en français
    month_names = {
        1: 'Janvier', 2: 'Février', 3: 'Mars', 4: 'Avril',
        5: 'Mai', 6: 'Juin', 7: 'Juillet', 8: 'Août',
        9: 'Septembre', 10: 'Octobre', 11: 'Novembre', 12: 'Décembre'
    }

    return render_template('agenda/index.html',
                          title='Agenda des Formateurs',
                          agendas=agendas,
                          calendar=calendar_with_events,
                          current_month=current_month,
                          current_year=current_year,
                          current_day=current_date.day,
                          display_month=current_month,
                          display_year=current_year,
                          month_name=month_names[current_month])

@login_required
def new_agenda():
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = AgendaFormateurForm()
    form.formateur.choices = [(f.id, f.nom_prenom) for f in Formateur.query.all()]

    if form.validate_on_submit():
        # Vérifier si un rendez-vous existe déjà pour ce formateur à cette date
        existing_agenda = AgendaFormateur.query.filter_by(
            formateur_id=form.formateur.data,
            date_rendezvous=form.date_rendezvous.data
        ).first()

        if existing_agenda:
            flash(f'Un rendez-vous existe déjà pour ce formateur à cette date.', 'danger')
            return render_template('agenda/new.html', title='Nouveau Rendez-vous', form=form)

        agenda = AgendaFormateur(
            formateur_id=form.formateur.data,
            date_rendezvous=form.date_rendezvous.data,
            description=form.description.data
        )
        db.session.add(agenda)
        db.session.commit()
        flash('Le rendez-vous a été créé avec succès!', 'success')
        return redirect(url_for('agenda_formateurs'))

    return render_template('agenda/new.html', title='Nouveau Rendez-vous', form=form)

@login_required
def edit_agenda(id):
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    agenda = AgendaFormateur.query.get_or_404(id)
    form = AgendaFormateurForm()
    form.formateur.choices = [(f.id, f.nom_prenom) for f in Formateur.query.all()]

    if form.validate_on_submit():
        # Vérifier si un rendez-vous existe déjà pour ce formateur à cette date (sauf celui-ci)
        existing_agenda = AgendaFormateur.query.filter(
            AgendaFormateur.formateur_id == form.formateur.data,
            AgendaFormateur.date_rendezvous == form.date_rendezvous.data,
            AgendaFormateur.id != agenda.id
        ).first()

        if existing_agenda:
            flash(f'Un rendez-vous existe déjà pour ce formateur à cette date.', 'danger')
            return render_template('agenda/edit.html', title='Modifier Rendez-vous', form=form, agenda=agenda)

        agenda.formateur_id = form.formateur.data
        agenda.date_rendezvous = form.date_rendezvous.data
        agenda.description = form.description.data

        db.session.commit()
        flash('Le rendez-vous a été modifié avec succès!', 'success')
        return redirect(url_for('agenda_formateurs'))

    if request.method == 'GET':
        form.formateur.data = agenda.formateur_id
        form.date_rendezvous.data = agenda.date_rendezvous
        form.description.data = agenda.description

    return render_template('agenda/edit.html', title='Modifier Rendez-vous', form=form, agenda=agenda)

@login_required
def delete_agenda(id):
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    agenda = AgendaFormateur.query.get_or_404(id)
    db.session.delete(agenda)
    db.session.commit()
    flash('Le rendez-vous a été supprimé avec succès!', 'success')
    return redirect(url_for('agenda_formateurs'))

# Routes pour les organismes
@login_required
def organismes():
    if not check_permission('organisme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    organismes = Organisme.query.all()
    return render_template('organismes/index.html', title='Organismes', organismes=organismes)

# Route pour générer des rapports
@login_required
def generate_report(type):
    from datetime import datetime

    # Vérifier les permissions
    if type == 'users' and not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))
    elif type == 'fiches_inscription' and not check_permission('fiche_inscription'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))
    elif type == 'dossiers_techniques' and not check_permission('dossier_technique'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))
    elif type == 'remboursements' and not check_permission('dossier_remboursement'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))
    elif type == 'organismes' and not check_permission('organisme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))
    elif type == 'formateurs' and not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))
    elif type == 'agenda' and not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))
    elif type == 'domaines_themes' and not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    # Préparer le contenu du rapport en fonction du type
    if type == 'users':
        title = 'Rapport des Utilisateurs'
        users = User.query.all()

        # Créer un tableau HTML
        content = '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Nom d\'utilisateur</th><th>Email</th><th>Nom complet</th><th>Admin</th><th>Permissions</th></tr></thead>'
        content += '<tbody>'

        for user in users:
            permissions = []
            if user.perm_fiche_inscription:
                permissions.append('Fiche d\'inscription')
            if user.perm_dossier_technique:
                permissions.append('Dossier technique')
            if user.perm_dossier_remboursement:
                permissions.append('Dossier de remboursement')
            if user.perm_organisme:
                permissions.append('Organisme')
            if user.perm_formateur:
                permissions.append('Formateur')
            if user.perm_agenda:
                permissions.append('Agenda')
            if user.perm_domaine_theme:
                permissions.append('Domaines et Thèmes')

            content += f'<tr><td>{user.id}</td><td>{user.username}</td><td>{user.email}</td><td>{user.nom_complet}</td>'
            content += f'<td>{"Oui" if user.is_admin else "Non"}</td><td>{", ".join(permissions)}</td></tr>'

        content += '</tbody></table>'

    elif type == 'formations':
        title = 'Rapport des Formations'
        formations = Formation.query.all()

        content = '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Thème</th><th>Date</th><th>Organisme</th></tr></thead>'
        content += '<tbody>'

        for formation in formations:
            content += f'<tr><td>{formation.id}</td><td>{formation.theme}</td>'
            content += f'<td>{formation.date.strftime("%d/%m/%Y")}</td>'
            content += f'<td>{formation.organisme.raison_sociale if formation.organisme else ""}</td></tr>'

        content += '</tbody></table>'

    elif type == 'fiches_inscription':
        title = 'Rapport des Fiches d\'Inscription'
        fiches = FicheInscription.query.all()

        content = '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Raison Sociale</th><th>Email</th><th>Téléphone</th><th>Éligible</th><th>Validation</th></tr></thead>'
        content += '<tbody>'

        for fiche in fiches:
            content += f'<tr><td>{fiche.id}</td><td>{fiche.raison_sociale}</td><td>{fiche.email}</td>'
            content += f'<td>{fiche.tel_entreprise}</td><td>{"Oui" if fiche.eligible else "Non"}</td>'
            content += f'<td>{"Oui" if fiche.validation else "Non"}</td></tr>'

        content += '</tbody></table>'

    elif type == 'dossiers_techniques':
        title = 'Rapport des Dossiers Techniques'
        dossiers = DossierTechnique.query.all()

        content = '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Entreprise</th><th>Domaine</th><th>Thème</th><th>Coût HT</th><th>Conforme</th></tr></thead>'
        content += '<tbody>'

        for dossier in dossiers:
            content += f'<tr><td>{dossier.id}</td>'
            content += f'<td>{dossier.fiche_inscription.raison_sociale if dossier.fiche_inscription else ""}</td>'
            content += f'<td>{dossier.domaine.nom if dossier.domaine else ""}</td>'
            content += f'<td>{dossier.theme.nom if dossier.theme else ""}</td>'
            content += f'<td>{dossier.cout_formation_ht}</td>'
            content += f'<td>{"Oui" if dossier.conforme else "Non"}</td></tr>'

        content += '</tbody></table>'

    elif type == 'remboursements':
        title = 'Rapport des Dossiers de Remboursement'
        remboursements = DossierRemboursement.query.all()

        content = '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Entreprise</th><th>Organisme</th><th>Formateur</th><th>Thème</th><th>Date</th></tr></thead>'
        content += '<tbody>'

        for remboursement in remboursements:
            content += f'<tr><td>{remboursement.id}</td>'
            content += f'<td>{remboursement.fiche_inscription.raison_sociale if remboursement.fiche_inscription else ""}</td>'
            content += f'<td>{remboursement.organisme.raison_sociale if remboursement.organisme else ""}</td>'
            content += f'<td>{remboursement.formateur.nom_prenom if remboursement.formateur else ""}</td>'
            content += f'<td>{remboursement.theme}</td>'
            content += f'<td>{remboursement.date.strftime("%d/%m/%Y") if remboursement.date else ""}</td></tr>'

        content += '</tbody></table>'

    elif type == 'organismes':
        title = 'Rapport des Organismes'
        organismes = Organisme.query.all()

        content = '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Raison Sociale</th><th>Forme Juridique</th><th>Gérant</th><th>Ville</th><th>Téléphone</th></tr></thead>'
        content += '<tbody>'

        for organisme in organismes:
            content += f'<tr><td>{organisme.id}</td><td>{organisme.raison_sociale}</td>'
            content += f'<td>{organisme.forme_juridique}</td><td>{organisme.nom_prenom_gerant}</td>'
            content += f'<td>{organisme.ville}</td><td>{organisme.telephone}</td></tr>'

        content += '</tbody></table>'

    elif type == 'formateurs':
        title = 'Rapport des Formateurs'
        formateurs = Formateur.query.all()

        content = '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Nom et Prénom</th><th>Spécialité</th><th>Ville</th><th>Téléphone</th><th>Email</th></tr></thead>'
        content += '<tbody>'

        for formateur in formateurs:
            content += f'<tr><td>{formateur.id}</td><td>{formateur.nom_prenom}</td>'
            content += f'<td>{formateur.specialite}</td><td>{formateur.ville}</td>'
            content += f'<td>{formateur.telephone}</td><td>{formateur.email}</td></tr>'

        content += '</tbody></table>'

    elif type == 'agenda':
        title = 'Rapport de l\'Agenda des Formateurs'
        agendas = AgendaFormateur.query.order_by(AgendaFormateur.date_rendezvous).all()

        content = '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Formateur</th><th>Date</th><th>Description</th></tr></thead>'
        content += '<tbody>'

        for agenda in agendas:
            content += f'<tr><td>{agenda.id}</td>'
            content += f'<td>{agenda.formateur.nom_prenom if agenda.formateur else ""}</td>'
            content += f'<td>{agenda.date_rendezvous.strftime("%d/%m/%Y")}</td>'
            content += f'<td>{agenda.description}</td></tr>'

        content += '</tbody></table>'

    elif type == 'domaines_themes':
        title = 'Rapport des Domaines et Thèmes'
        domaines = Domaine.query.all()

        content = '<h3>Domaines</h3>'
        content += '<table class="table table-bordered table-striped mb-4">'
        content += '<thead><tr><th>ID</th><th>Nom</th></tr></thead>'
        content += '<tbody>'

        for domaine in domaines:
            content += f'<tr><td>{domaine.id}</td><td>{domaine.nom}</td></tr>'

        content += '</tbody></table>'

        content += '<h3>Thèmes</h3>'
        content += '<table class="table table-bordered table-striped">'
        content += '<thead><tr><th>ID</th><th>Nom</th><th>Domaine</th></tr></thead>'
        content += '<tbody>'

        themes = Theme.query.all()
        for theme in themes:
            content += f'<tr><td>{theme.id}</td><td>{theme.nom}</td>'
            content += f'<td>{theme.domaine.nom if theme.domaine else ""}</td></tr>'

        content += '</tbody></table>'

    else:
        flash('Type de rapport non valide.', 'danger')
        return redirect(url_for('index'))

    # Enregistrer le rapport dans la base de données
    rapport = Rapport(
        type_rapport=type,
        titre=title,
        contenu=content,
        date_creation=datetime.now(),
        user_id=current_user.id
    )
    db.session.add(rapport)
    db.session.commit()

    # Rendre le template avec le contenu du rapport
    return render_template('reports/report.html',
                          title=title,
                          content=content,
                          date_creation=datetime.now(),
                          current_year=datetime.now().year,
                          current_user=current_user)

@login_required
def new_organisme():
    if not check_permission('organisme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = OrganismeForm()

    if form.validate_on_submit():
        # Traitement du fichier joint
        piece_jointe = None
        if form.piece_jointe.data:
            piece_jointe = save_file(form.piece_jointe.data)

        organisme = Organisme(
            raison_sociale=form.raison_sociale.data,
            forme_juridique=form.forme_juridique.data,
            date_creation=form.date_creation.data,
            nom_prenom_gerant=form.nom_prenom_gerant.data,
            adresse=form.adresse.data,
            ville=form.ville.data,
            telephone=form.telephone.data,
            fax=form.fax.data,
            email=form.email.data,
            patente=form.patente.data,
            identifiant_fiscal=form.identifiant_fiscal.data,
            num_rc=form.num_rc.data,
            num_cnss=form.num_cnss.data,
            piece_jointe=piece_jointe
        )
        db.session.add(organisme)
        db.session.commit()
        flash('L\'organisme a été créé avec succès!', 'success')
        return redirect(url_for('organismes'))

    return render_template('organismes/new.html', title='Nouvel Organisme', form=form)

@login_required
def edit_organisme(id):
    if not check_permission('organisme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    organisme = Organisme.query.get_or_404(id)
    form = OrganismeForm()

    if form.validate_on_submit():
        organisme.raison_sociale = form.raison_sociale.data
        organisme.forme_juridique = form.forme_juridique.data
        organisme.date_creation = form.date_creation.data
        organisme.nom_prenom_gerant = form.nom_prenom_gerant.data
        organisme.adresse = form.adresse.data
        organisme.ville = form.ville.data
        organisme.telephone = form.telephone.data
        organisme.fax = form.fax.data
        organisme.email = form.email.data
        organisme.patente = form.patente.data
        organisme.identifiant_fiscal = form.identifiant_fiscal.data
        organisme.num_rc = form.num_rc.data
        organisme.num_cnss = form.num_cnss.data

        # Traitement du fichier joint
        if form.piece_jointe.data:
            organisme.piece_jointe = save_file(form.piece_jointe.data)

        db.session.commit()
        flash('L\'organisme a été modifié avec succès!', 'success')
        return redirect(url_for('organismes'))

    if request.method == 'GET':
        form.raison_sociale.data = organisme.raison_sociale
        form.forme_juridique.data = organisme.forme_juridique
        form.date_creation.data = organisme.date_creation
        form.nom_prenom_gerant.data = organisme.nom_prenom_gerant
        form.adresse.data = organisme.adresse
        form.ville.data = organisme.ville
        form.telephone.data = organisme.telephone
        form.fax.data = organisme.fax
        form.email.data = organisme.email
        form.patente.data = organisme.patente
        form.identifiant_fiscal.data = organisme.identifiant_fiscal
        form.num_rc.data = organisme.num_rc
        form.num_cnss.data = organisme.num_cnss

    return render_template('organismes/edit.html', title='Modifier Organisme', form=form, organisme=organisme)

@login_required
def delete_organisme(id):
    if not check_permission('organisme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    organisme = Organisme.query.get_or_404(id)

    # Vérifier si l'organisme est utilisé dans des formations
    if Formation.query.filter_by(organisme_id=organisme.id).count() > 0:
        flash('Cet organisme ne peut pas être supprimé car il est utilisé dans des formations.', 'danger')
        return redirect(url_for('organismes'))

    # Vérifier si l'organisme est utilisé dans des dossiers techniques
    if DossierTechnique.query.filter_by(organisme_formation_id=organisme.id).count() > 0:
        flash('Cet organisme ne peut pas être supprimé car il est utilisé dans des dossiers techniques.', 'danger')
        return redirect(url_for('organismes'))

    # Vérifier si l'organisme est utilisé dans des dossiers de remboursement
    if DossierRemboursement.query.filter_by(organisme_id=organisme.id).count() > 0:
        flash('Cet organisme ne peut pas être supprimé car il est utilisé dans des dossiers de remboursement.', 'danger')
        return redirect(url_for('organismes'))

    db.session.delete(organisme)
    db.session.commit()
    flash('L\'organisme a été supprimé avec succès!', 'success')
    return redirect(url_for('organismes'))

@login_required
def formateurs():
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    formateurs = Formateur.query.all()
    return render_template('formateurs/index.html', title='Formateurs', formateurs=formateurs)

@login_required
def new_formateur():
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = FormateurForm()

    if form.validate_on_submit():
        # Traitement du fichier joint
        piece_jointe = None
        if form.piece_jointe.data:
            piece_jointe = save_file(form.piece_jointe.data)

        formateur = Formateur(
            nom_prenom=form.nom_prenom.data,
            specialite=form.specialite.data,
            cv=form.cv.data,
            diplomes=form.diplomes.data,
            adresse=form.adresse.data,
            ville=form.ville.data,
            num_tel=form.num_tel.data,
            email=form.email.data,
            piece_jointe=piece_jointe
        )
        db.session.add(formateur)
        db.session.commit()
        flash('Le formateur a été créé avec succès!', 'success')
        return redirect(url_for('formateurs'))

    return render_template('formateurs/new.html', title='Nouveau Formateur', form=form)

@login_required
def edit_formateur(id):
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    formateur = Formateur.query.get_or_404(id)
    form = FormateurForm()

    if form.validate_on_submit():
        formateur.nom_prenom = form.nom_prenom.data
        formateur.specialite = form.specialite.data
        formateur.cv = form.cv.data
        formateur.diplomes = form.diplomes.data
        formateur.adresse = form.adresse.data
        formateur.ville = form.ville.data
        formateur.num_tel = form.num_tel.data
        formateur.email = form.email.data

        # Traitement du fichier joint
        if form.piece_jointe.data:
            formateur.piece_jointe = save_file(form.piece_jointe.data)

        db.session.commit()
        flash('Le formateur a été modifié avec succès!', 'success')
        return redirect(url_for('formateurs'))

    if request.method == 'GET':
        form.nom_prenom.data = formateur.nom_prenom
        form.specialite.data = formateur.specialite
        form.cv.data = formateur.cv
        form.diplomes.data = formateur.diplomes
        form.adresse.data = formateur.adresse
        form.ville.data = formateur.ville
        form.num_tel.data = formateur.num_tel
        form.email.data = formateur.email

    return render_template('formateurs/edit.html', title='Modifier Formateur', form=form, formateur=formateur)

@login_required
def delete_formateur(id):
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    formateur = Formateur.query.get_or_404(id)

    # Vérifier si le formateur est utilisé dans des dossiers de remboursement
    if DossierRemboursement.query.filter_by(formateur_id=formateur.id).count() > 0:
        flash('Ce formateur ne peut pas être supprimé car il est utilisé dans des dossiers de remboursement.', 'danger')
        return redirect(url_for('formateurs'))

    # Vérifier si le formateur a des rendez-vous dans l'agenda
    if AgendaFormateur.query.filter_by(formateur_id=formateur.id).count() > 0:
        flash('Ce formateur ne peut pas être supprimé car il a des rendez-vous dans l\'agenda.', 'danger')
        return redirect(url_for('formateurs'))

    db.session.delete(formateur)
    db.session.commit()
    flash('Le formateur a été supprimé avec succès!', 'success')
    return redirect(url_for('formateurs'))

# Routes pour l'agenda des formateurs
@login_required
def agenda_formateurs():
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    # Récupérer le mois et l'année à afficher (par défaut: mois et année actuels)
    from datetime import datetime
    import calendar

    current_month = datetime.now().month
    current_year = datetime.now().year
    current_day = datetime.now().day

    # Récupérer les paramètres de l'URL
    month = request.args.get('month', type=int)
    year = request.args.get('year', type=int)

    # Si les paramètres sont fournis, les utiliser
    display_month = month if month else current_month
    display_year = year if year else current_year

    # Nom du mois en français
    month_names = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                  'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']
    month_name = month_names[display_month - 1]

    # Générer le calendrier pour le mois
    cal = calendar.monthcalendar(display_year, display_month)

    # Récupérer tous les rendez-vous du mois
    from datetime import date
    start_date = date(display_year, display_month, 1)
    if display_month == 12:
        end_date = date(display_year + 1, 1, 1)
    else:
        end_date = date(display_year, display_month + 1, 1)

    month_agendas = AgendaFormateur.query.filter(
        AgendaFormateur.date_rendezvous >= start_date,
        AgendaFormateur.date_rendezvous < end_date
    ).all()

    # Organiser les rendez-vous par jour
    events_by_day = {}
    for agenda in month_agendas:
        day = agenda.date_rendezvous.day
        if day not in events_by_day:
            events_by_day[day] = []
        events_by_day[day].append(agenda)

    # Ajouter les événements au calendrier
    calendar_with_events = []
    for week in cal:
        week_with_events = []
        for day in week:
            if day == 0:
                week_with_events.append((0, []))
            else:
                week_with_events.append((day, events_by_day.get(day, [])))
        calendar_with_events.append(week_with_events)

    # Récupérer tous les rendez-vous pour la liste
    agendas = AgendaFormateur.query.order_by(AgendaFormateur.date_rendezvous.desc()).all()

    return render_template('agenda/index.html',
                          title='Agenda des Formateurs',
                          agendas=agendas,
                          calendar=calendar_with_events,
                          current_month=current_month,
                          current_year=current_year,
                          current_day=current_day,
                          display_month=display_month,
                          display_year=display_year,
                          month_name=month_name)

@login_required
def new_agenda():
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = AgendaFormateurForm()
    form.formateur.choices = [(f.id, f.nom_prenom) for f in Formateur.query.all()]

    if form.validate_on_submit():
        # Vérifier si le formateur a déjà un rendez-vous à cette date
        from datetime import datetime

        # Vérifier si form.date_rendezvous.data est déjà un objet date ou datetime
        if isinstance(form.date_rendezvous.data, datetime):
            date_only = form.date_rendezvous.data.date()
        else:
            # Si c'est déjà un objet date, l'utiliser directement
            date_only = form.date_rendezvous.data

        # Rechercher les rendez-vous existants pour ce formateur à cette date
        existing_agendas = AgendaFormateur.query.filter(
            AgendaFormateur.formateur_id == form.formateur.data,
            AgendaFormateur.date_rendezvous >= datetime.combine(date_only, datetime.min.time()),
            AgendaFormateur.date_rendezvous < datetime.combine(date_only, datetime.max.time())
        ).all()

        if existing_agendas:
            flash('Ce formateur a déjà un rendez-vous à cette date! Veuillez choisir une autre date ou un autre formateur.', 'danger')
            return render_template('agenda/new.html', title='Nouveau Rendez-vous', form=form)

        agenda = AgendaFormateur(
            formateur_id=form.formateur.data,
            date_rendezvous=form.date_rendezvous.data,
            description=form.description.data
        )
        db.session.add(agenda)
        db.session.commit()
        flash('Le rendez-vous a été créé avec succès!', 'success')
        return redirect(url_for('agenda_formateurs'))

    return render_template('agenda/new.html', title='Nouveau Rendez-vous', form=form)

@login_required
def edit_agenda(id):
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    agenda = AgendaFormateur.query.get_or_404(id)
    form = AgendaFormateurForm()
    form.formateur.choices = [(f.id, f.nom_prenom) for f in Formateur.query.all()]

    if form.validate_on_submit():
        # Vérifier si le formateur a déjà un rendez-vous à cette date (sauf celui-ci)
        from datetime import datetime

        # Vérifier si form.date_rendezvous.data est déjà un objet date ou datetime
        if isinstance(form.date_rendezvous.data, datetime):
            date_only = form.date_rendezvous.data.date()
        else:
            # Si c'est déjà un objet date, l'utiliser directement
            date_only = form.date_rendezvous.data

        # Rechercher les rendez-vous existants pour ce formateur à cette date
        existing_agendas = AgendaFormateur.query.filter(
            AgendaFormateur.formateur_id == form.formateur.data,
            AgendaFormateur.date_rendezvous >= datetime.combine(date_only, datetime.min.time()),
            AgendaFormateur.date_rendezvous < datetime.combine(date_only, datetime.max.time()),
            AgendaFormateur.id != agenda.id
        ).all()

        if existing_agendas:
            flash('Ce formateur a déjà un rendez-vous à cette date! Veuillez choisir une autre date ou un autre formateur.', 'danger')
            return render_template('agenda/edit.html', title='Modifier Rendez-vous', form=form, agenda=agenda)

        agenda.formateur_id = form.formateur.data
        agenda.date_rendezvous = form.date_rendezvous.data
        agenda.description = form.description.data
        db.session.commit()
        flash('Le rendez-vous a été modifié avec succès!', 'success')
        return redirect(url_for('agenda_formateurs'))

    if request.method == 'GET':
        form.formateur.data = agenda.formateur_id
        form.date_rendezvous.data = agenda.date_rendezvous
        form.description.data = agenda.description

    return render_template('agenda/edit.html', title='Modifier Rendez-vous', form=form, agenda=agenda)

@login_required
def delete_agenda(id):
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    agenda = AgendaFormateur.query.get_or_404(id)
    db.session.delete(agenda)
    db.session.commit()
    flash('Le rendez-vous a été supprimé avec succès!', 'success')
    return redirect(url_for('agenda_formateurs'))

@login_required
def new_formation():
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = FormationForm()
    # Populate organisme choices before validating the form
    form.organisme.choices = [(o.id, o.raison_sociale) for o in Organisme.query.all()]

    if form.validate_on_submit():
        formation = Formation(
            theme=form.theme.data,
            date=form.date.data,
            organisme_id=form.organisme.data
        )
        db.session.add(formation)
        db.session.commit()
        flash('La formation a été enregistrée avec succès!', 'success')
        return redirect(url_for('formations'))

    return redirect(url_for('dashboard'))

@login_required
def edit_formation(id):
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    formation = Formation.query.get_or_404(id)
    form = FormationForm()
    form.organisme.choices = [(o.id, o.raison_sociale) for o in Organisme.query.all()]

    if form.validate_on_submit():
        formation.theme = form.theme.data
        formation.date = form.date.data
        formation.organisme_id = form.organisme.data
        db.session.commit()
        flash('La formation a été modifiée avec succès!', 'success')
        return redirect(url_for('formations'))

    # Pré-remplir le formulaire avec les données existantes
    if request.method == 'GET':
        form.theme.data = formation.theme
        form.date.data = formation.date
        form.organisme.data = formation.organisme_id

    return redirect(url_for('dashboard'))

@login_required
def delete_formation(id):
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    formation = Formation.query.get_or_404(id)
    db.session.delete(formation)
    db.session.commit()
    flash('La formation a été supprimée avec succès!', 'success')
    return redirect(url_for('formations'))

# Routes pour la gestion des utilisateurs
@login_required
def users():
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    users = User.query.all()
    return render_template('users/index.html', title='Utilisateurs', users=users)

@login_required
def new_user():
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = UserForm()

    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            nom_complet=form.nom_complet.data,
            is_admin=form.is_admin.data,
            date_creation=datetime.now(timezone.utc),
            perm_fiche_inscription=form.perm_fiche_inscription.data,
            perm_dossier_technique=form.perm_dossier_technique.data,
            perm_dossier_remboursement=form.perm_dossier_remboursement.data,
            perm_organisme=form.perm_organisme.data,
            perm_formateur=form.perm_formateur.data,
            perm_agenda=form.perm_agenda.data,
            perm_domaine_theme=form.perm_domaine_theme.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()

        # Créer une notification
        notification = Notification(
            message=f"Nouvel utilisateur créé: {user.username}",
            user_id=current_user.id
        )
        db.session.add(notification)
        db.session.commit()

        flash('L\'utilisateur a été créé avec succès!', 'success')
        return redirect(url_for('users'))

    return render_template('users/new.html', title='Nouvel Utilisateur', form=form)

@login_required
def edit_user(id):
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    user = User.query.get_or_404(id)
    form = UserForm()

    # Stocker l'ID de l'utilisateur pour la validation
    form.user_id = user.id

    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.nom_complet = form.nom_complet.data
        user.is_admin = form.is_admin.data
        user.perm_fiche_inscription = form.perm_fiche_inscription.data
        user.perm_dossier_technique = form.perm_dossier_technique.data
        user.perm_dossier_remboursement = form.perm_dossier_remboursement.data
        user.perm_organisme = form.perm_organisme.data
        user.perm_formateur = form.perm_formateur.data
        user.perm_agenda = form.perm_agenda.data
        user.perm_domaine_theme = form.perm_domaine_theme.data

        if form.password.data:
            user.set_password(form.password.data)

        db.session.commit()
        flash('L\'utilisateur a été modifié avec succès!', 'success')
        return redirect(url_for('users'))

    # Pré-remplir le formulaire avec les données existantes
    if request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.nom_complet.data = user.nom_complet
        form.is_admin.data = user.is_admin
        form.perm_fiche_inscription.data = user.perm_fiche_inscription
        form.perm_dossier_technique.data = user.perm_dossier_technique
        form.perm_dossier_remboursement.data = user.perm_dossier_remboursement
        form.perm_organisme.data = user.perm_organisme
        form.perm_formateur.data = user.perm_formateur
        form.perm_agenda.data = user.perm_agenda
        form.perm_domaine_theme.data = user.perm_domaine_theme

    return render_template('users/edit.html', title='Modifier Utilisateur', form=form, user=user)

@login_required
def delete_user(id):
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    user = User.query.get_or_404(id)

    # Ne pas permettre de supprimer son propre compte
    if user.id == current_user.id:
        flash('Vous ne pouvez pas supprimer votre propre compte!', 'danger')
        return redirect(url_for('users'))

    db.session.delete(user)
    db.session.commit()
    flash('L\'utilisateur a été supprimé avec succès!', 'success')
    return redirect(url_for('users'))

# Routes pour les domaines et thèmes
@login_required
def domaines():
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    domaines = Domaine.query.all()
    return render_template('domaines/index.html', title='Domaines', domaines=domaines)

@login_required
def new_domaine():
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = DomaineForm()

    if form.validate_on_submit():
        domaine = Domaine(nom=form.nom.data)
        db.session.add(domaine)
        db.session.commit()
        flash('Le domaine a été créé avec succès!', 'success')
        return redirect(url_for('domaines_themes'))

    return render_template('domaines/new_domaine.html', title='Nouveau Domaine', form=form)

@login_required
def edit_domaine(id):
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    domaine = Domaine.query.get_or_404(id)
    form = DomaineForm()

    if form.validate_on_submit():
        domaine.nom = form.nom.data
        db.session.commit()
        flash('Le domaine a été modifié avec succès!', 'success')
        return redirect(url_for('domaines_themes'))

    if request.method == 'GET':
        form.nom.data = domaine.nom

    return render_template('domaines/edit_domaine.html', title='Modifier Domaine', form=form, domaine=domaine)

@login_required
def delete_domaine(id):
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    domaine = Domaine.query.get_or_404(id)

    # Vérifier si le domaine est utilisé par des thèmes
    if domaine.themes.count() > 0:
        flash('Ce domaine ne peut pas être supprimé car il est utilisé par des thèmes.', 'danger')
        return redirect(url_for('domaines_themes'))

    db.session.delete(domaine)
    db.session.commit()
    flash('Le domaine a été supprimé avec succès!', 'success')
    return redirect(url_for('domaines_themes'))

@login_required
def new_theme():
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    form = ThemeForm()
    form.domaine.choices = [(d.id, d.nom) for d in Domaine.query.all()]

    if form.validate_on_submit():
        theme = Theme(nom=form.nom.data, domaine_id=form.domaine.data)
        db.session.add(theme)
        db.session.commit()
        flash('Le thème a été créé avec succès!', 'success')
        return redirect(url_for('domaines_themes'))

    return render_template('domaines/new_theme.html', title='Nouveau Thème', form=form)

@login_required
def edit_theme(id):
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    theme = Theme.query.get_or_404(id)
    form = ThemeForm()
    form.domaine.choices = [(d.id, d.nom) for d in Domaine.query.all()]

    if form.validate_on_submit():
        theme.nom = form.nom.data
        theme.domaine_id = form.domaine.data
        db.session.commit()
        flash('Le thème a été modifié avec succès!', 'success')
        return redirect(url_for('domaines_themes'))

    if request.method == 'GET':
        form.nom.data = theme.nom
        form.domaine.data = theme.domaine_id

    return render_template('domaines/edit_theme.html', title='Modifier Thème', form=form, theme=theme)

@login_required
def delete_theme(id):
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    theme = Theme.query.get_or_404(id)

    # Vérifier si le thème est utilisé par des dossiers techniques
    if DossierTechnique.query.filter_by(theme_id=theme.id).count() > 0:
        flash('Ce thème ne peut pas être supprimé car il est utilisé par des dossiers techniques.', 'danger')
        return redirect(url_for('domaines_themes'))

    db.session.delete(theme)
    db.session.commit()
    flash('Le thème a été supprimé avec succès!', 'success')
    return redirect(url_for('domaines_themes'))

# Routes pour les rapports et l'impression
@login_required
def rapports():
    rapports = Rapport.query.filter_by(user_id=current_user.id).order_by(Rapport.date_creation.desc()).all()
    return render_template('rapports/index.html', title='Rapports', rapports=rapports)

# Routes pour l'impression des fiches
@login_required
def print_fiche_inscription(id):
    if not check_permission('fiche_inscription'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    fiche = FicheInscription.query.get_or_404(id)
    from datetime import datetime
    return render_template('fiches_inscription/print.html',
                          title=f'Fiche d\'Inscription - {fiche.raison_sociale}',
                          fiche=fiche,
                          now=datetime.now(),
                          back_url=url_for('fiches_inscription'))

@login_required
def print_dossier_technique(id):
    if not check_permission('dossier_technique'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    dossier = DossierTechnique.query.get_or_404(id)
    from datetime import datetime
    return render_template('dossiers_techniques/print.html',
                          title=f'Dossier Technique - {dossier.theme.nom if dossier.theme else "N/A"}',
                          dossier=dossier,
                          now=datetime.now(),
                          back_url=url_for('dossiers_techniques'))

@login_required
def print_remboursement(id):
    if not check_permission('dossier_remboursement'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    remboursement = DossierRemboursement.query.get_or_404(id)
    from datetime import datetime
    return render_template('remboursements/print.html',
                          title=f'Dossier de Remboursement - {remboursement.theme}',
                          remboursement=remboursement,
                          now=datetime.now(),
                          back_url=url_for('remboursements'))

@login_required
def print_organisme(id):
    if not check_permission('organisme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    organisme = Organisme.query.get_or_404(id)
    from datetime import datetime
    return render_template('organismes/print.html',
                          title=f'Organisme - {organisme.raison_sociale}',
                          organisme=organisme,
                          now=datetime.now(),
                          back_url=url_for('organismes'))

@login_required
def print_formateur(id):
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    formateur = Formateur.query.get_or_404(id)
    from datetime import datetime
    return render_template('formateurs/print.html',
                          title=f'Formateur - {formateur.nom_prenom}',
                          formateur=formateur,
                          now=datetime.now(),
                          back_url=url_for('formateurs'))

@login_required
def print_agenda(id):
    if not check_permission('agenda'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    agenda = AgendaFormateur.query.get_or_404(id)
    from datetime import datetime
    return render_template('agenda/print.html',
                          title=f'Rendez-vous - {agenda.formateur.nom_prenom} - {agenda.date_rendezvous.strftime("%d/%m/%Y")}',
                          agenda=agenda,
                          now=datetime.now(),
                          back_url=url_for('agenda_formateurs'))

@login_required
def new_rapport():
    form = RapportForm()

    if form.validate_on_submit():
        # Générer le contenu du rapport en fonction du type
        contenu = ""
        if form.type_rapport.data == 'fiche_inscription':
            fiches = FicheInscription.query.all()
            contenu = render_template('rapports/fiche_inscription.html', fiches=fiches)
        elif form.type_rapport.data == 'dossier_technique':
            dossiers = DossierTechnique.query.all()
            contenu = render_template('rapports/dossier_technique.html', dossiers=dossiers)
        elif form.type_rapport.data == 'dossier_remboursement':
            remboursements = DossierRemboursement.query.all()
            contenu = render_template('rapports/dossier_remboursement.html', remboursements=remboursements)
        elif form.type_rapport.data == 'organisme':
            organismes = Organisme.query.all()
            contenu = render_template('rapports/organisme.html', organismes=organismes)
        elif form.type_rapport.data == 'formateur':
            formateurs = Formateur.query.all()
            contenu = render_template('rapports/formateur.html', formateurs=formateurs)
        elif form.type_rapport.data == 'agenda':
            agendas = AgendaFormateur.query.all()
            contenu = render_template('rapports/agenda.html', agendas=agendas)
        elif form.type_rapport.data == 'domaine_theme':
            domaines = Domaine.query.all()
            themes = Theme.query.all()
            contenu = render_template('rapports/domaine_theme.html', domaines=domaines, themes=themes)

        rapport = Rapport(
            titre=form.titre.data,
            type_rapport=form.type_rapport.data,
            contenu=contenu,
            user_id=current_user.id
        )
        db.session.add(rapport)
        db.session.commit()
        flash('Le rapport a été généré avec succès!', 'success')
        return redirect(url_for('view_rapport', id=rapport.id))

    return render_template('rapports/new.html', title='Nouveau Rapport', form=form)

@login_required
def view_rapport(id):
    rapport = Rapport.query.get_or_404(id)

    # Vérifier si l'utilisateur est le propriétaire du rapport ou un admin
    if rapport.user_id != current_user.id and not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à ce rapport.', 'danger')
        return redirect(url_for('rapports'))

    return render_template('rapports/view.html', title=rapport.titre, rapport=rapport)

@login_required
def print_rapport(id):
    rapport = Rapport.query.get_or_404(id)

    # Vérifier si l'utilisateur est le propriétaire du rapport ou un admin
    if rapport.user_id != current_user.id and not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à ce rapport.', 'danger')
        return redirect(url_for('rapports'))

    return render_template('rapports/print.html', title=rapport.titre, rapport=rapport)

@login_required
def delete_rapport(id):
    rapport = Rapport.query.get_or_404(id)

    # Vérifier si l'utilisateur est le propriétaire du rapport ou un admin
    if rapport.user_id != current_user.id and not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour supprimer ce rapport.', 'danger')
        return redirect(url_for('rapports'))

    db.session.delete(rapport)
    db.session.commit()
    flash('Le rapport a été supprimé avec succès!', 'success')
    return redirect(url_for('rapports'))

# Routes pour la recherche
@login_required
def search_fiches():
    if not check_permission('fiche_inscription'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    query = request.args.get('q', '')

    if query:
        fiches = FicheInscription.query.filter(
            or_(
                FicheInscription.raison_sociale.ilike(f'%{query}%'),
                FicheInscription.email.ilike(f'%{query}%'),
                FicheInscription.tel_entreprise.ilike(f'%{query}%'),
                FicheInscription.ice.ilike(f'%{query}%'),
                FicheInscription.num_cnss.ilike(f'%{query}%')
            )
        ).all()
    else:
        fiches = FicheInscription.query.all()

    return render_template('fiches_inscription/index.html',
                          title='Fiches d\'Inscription',
                          fiches=fiches,
                          query=query)

@login_required
def search_dossiers():
    if not check_permission('dossier_technique'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    query = request.args.get('q', '')

    if query:
        dossiers = DossierTechnique.query.filter(
            or_(
                DossierTechnique.objectif.ilike(f'%{query}%'),
                DossierTechnique.contenu_indicatif.ilike(f'%{query}%'),
                DossierTechnique.num_cnss_organisme.ilike(f'%{query}%'),
                DossierTechnique.type_formation.ilike(f'%{query}%')
            )
        ).all()
    else:
        dossiers = DossierTechnique.query.all()

    return render_template('dossiers_techniques/index.html',
                          title='Dossiers Techniques',
                          dossiers=dossiers,
                          query=query)

@login_required
def search_remboursements():
    if not check_permission('dossier_remboursement'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    query = request.args.get('q', '')

    if query:
        remboursements = DossierRemboursement.query.filter(
            or_(
                DossierRemboursement.theme.ilike(f'%{query}%'),
                DossierRemboursement.mode_de_reglement.ilike(f'%{query}%')
            )
        ).all()
    else:
        remboursements = DossierRemboursement.query.all()

    return render_template('remboursements/index.html',
                          title='Dossiers de Remboursement',
                          remboursements=remboursements,
                          query=query)

@login_required
def search_organismes():
    if not check_permission('organisme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    query = request.args.get('q', '')

    if query:
        organismes = Organisme.query.filter(
            or_(
                Organisme.raison_sociale.ilike(f'%{query}%'),
                Organisme.forme_juridique.ilike(f'%{query}%'),
                Organisme.nom_prenom_gerant.ilike(f'%{query}%'),
                Organisme.ville.ilike(f'%{query}%'),
                Organisme.telephone.ilike(f'%{query}%'),
                Organisme.email.ilike(f'%{query}%')
            )
        ).all()
    else:
        organismes = Organisme.query.all()

    return render_template('organismes/index.html',
                          title='Organismes',
                          organismes=organismes,
                          query=query)

@login_required
def search_formateurs():
    if not check_permission('formateur'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    query = request.args.get('q', '')

    if query:
        formateurs = Formateur.query.filter(
            or_(
                Formateur.nom_prenom.ilike(f'%{query}%'),
                Formateur.specialite.ilike(f'%{query}%'),
                Formateur.ville.ilike(f'%{query}%'),
                Formateur.num_tel.ilike(f'%{query}%'),
                Formateur.email.ilike(f'%{query}%')
            )
        ).all()
    else:
        formateurs = Formateur.query.all()

    return render_template('formateurs/index.html',
                          title='Formateurs',
                          formateurs=formateurs,
                          query=query)

@login_required
def search_domaines_themes():
    if not check_permission('domaine_theme'):
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    query = request.args.get('q', '')

    if query:
        domaines = Domaine.query.filter(Domaine.nom.ilike(f'%{query}%')).all()
        themes = Theme.query.filter(Theme.nom.ilike(f'%{query}%')).all()
    else:
        domaines = Domaine.query.all()
        themes = Theme.query.all()

    return render_template('domaines/index.html',
                          title='Domaines et Thèmes',
                          domaines=domaines,
                          themes=themes,
                          query=query)

@login_required
def search_users():
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('index'))

    query = request.args.get('q', '')

    if query:
        users = User.query.filter(
            or_(
                User.username.ilike(f'%{query}%'),
                User.email.ilike(f'%{query}%'),
                User.nom_complet.ilike(f'%{query}%')
            )
        ).all()
    else:
        users = User.query.all()

    return render_template('users/index.html',
                          title='Gestion des Utilisateurs',
                          users=users,
                          query=query)


# Fonction pour enregistrer les activités des utilisateurs
def log_user_activity(type_action, module_concerne, description_action=None):
    """Enregistrement de l'activité utilisateur"""
    if current_user.is_authenticated:
        activity = UserActivityLog(
            user_id=current_user.id,
            nom_utilisateur=current_user.username,
            type_action=type_action,
            module_concerne=module_concerne,
            description_action=description_action,
            adresse_ip=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(activity)
        try:
            db.session.commit()
        except:
            db.session.rollback()


# Routes des informations de l'entreprise
@login_required
def company_info():
    """Affichage et modification des informations de l'entreprise"""
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('dashboard'))

    company = CompanyInfo.query.first()
    form = CompanyInfoForm()

    if form.validate_on_submit():
        if not company:
            company = CompanyInfo()

        company.nom_entreprise = form.nom_entreprise.data
        company.slogan = form.slogan.data
        company.adresse = form.adresse.data
        company.telephone = form.telephone.data
        company.fax = form.fax.data
        company.email = form.email.data
        company.site_web = form.site_web.data
        company.pied_de_page = form.pied_de_page.data
        company.couleur_principale = form.couleur_principale.data
        company.couleur_secondaire = form.couleur_secondaire.data

        # Traitement du téléchargement du logo
        if form.logo.data:
            filename = secure_filename(form.logo.data.filename)
            if filename and allowed_file(filename):
                # Créer un dossier pour les logos s'il n'existe pas
                logo_folder = os.path.join(UPLOAD_FOLDER, 'logos')
                if not os.path.exists(logo_folder):
                    os.makedirs(logo_folder)

                # Sauvegarder le fichier
                logo_path = os.path.join(logo_folder, filename)
                form.logo.data.save(logo_path)
                company.logo_path = f'uploads/logos/{filename}'

        if not company.id:
            db.session.add(company)

        db.session.commit()
        log_user_activity('update', 'company_info', 'Mise à jour des informations de l\'entreprise')
        flash('Les informations de l\'entreprise ont été enregistrées avec succès.', 'success')
        return redirect(url_for('company_info'))

    # Remplir le formulaire avec les données actuelles
    if company:
        form.nom_entreprise.data = company.nom_entreprise
        form.slogan.data = company.slogan
        form.adresse.data = company.adresse
        form.telephone.data = company.telephone
        form.fax.data = company.fax
        form.email.data = company.email
        form.site_web.data = company.site_web
        form.pied_de_page.data = company.pied_de_page
        form.couleur_principale.data = company.couleur_principale
        form.couleur_secondaire.data = company.couleur_secondaire

    return render_template('company/info.html',
                          title='Informations de l\'Entreprise',
                          form=form,
                          company=company)


# Routes pour le journal d'activité
@login_required
def activity_log():
    """Affichage du journal d'activité des utilisateurs"""
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('dashboard'))

    form = ActivityLogSearchForm()
    page = request.args.get('page', 1, type=int)

    # Construction de la requête
    query = UserActivityLog.query

    if form.validate_on_submit():
        if form.nom_utilisateur.data:
            query = query.filter(UserActivityLog.nom_utilisateur.ilike(f'%{form.nom_utilisateur.data}%'))
        if form.type_action.data:
            query = query.filter(UserActivityLog.type_action == form.type_action.data)
        if form.module_concerne.data:
            query = query.filter(UserActivityLog.module_concerne == form.module_concerne.data)
        if form.date_debut.data:
            query = query.filter(UserActivityLog.date_action >= form.date_debut.data)
        if form.date_fin.data:
            query = query.filter(UserActivityLog.date_action <= form.date_fin.data)

    activities = query.order_by(UserActivityLog.date_action.desc()).paginate(
        page=page, per_page=50, error_out=False
    )

    log_user_activity('view', 'activity_log', 'Consultation du journal d\'activité')

    return render_template('activity/log.html',
                          title='Journal d\'Activité',
                          activities=activities,
                          form=form)


# Routes pour les sauvegardes
@login_required
def backup_settings():
    """Paramètres de sauvegarde"""
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('dashboard'))

    settings = BackupSettings.query.first()
    form = BackupSettingsForm()

    if form.validate_on_submit():
        if not settings:
            settings = BackupSettings()

        settings.backup_automatique = form.backup_automatique.data
        settings.frequence_backup = form.frequence_backup.data
        settings.heure_backup = form.heure_backup.data
        settings.dossier_backup = form.dossier_backup.data
        settings.nombre_max_backups = form.nombre_max_backups.data
        settings.compression = form.compression.data
        settings.notification_email = form.notification_email.data
        settings.email_notification = form.email_notification.data

        if not settings.id:
            db.session.add(settings)

        db.session.commit()
        log_user_activity('update', 'backup', 'Mise à jour des paramètres de sauvegarde')
        flash('Les paramètres de sauvegarde ont été enregistrés avec succès.', 'success')
        return redirect(url_for('backup_settings'))

    # Remplir le formulaire avec les données actuelles
    if settings:
        form.backup_automatique.data = settings.backup_automatique
        form.frequence_backup.data = settings.frequence_backup
        form.heure_backup.data = settings.heure_backup
        form.dossier_backup.data = settings.dossier_backup
        form.nombre_max_backups.data = settings.nombre_max_backups
        form.compression.data = settings.compression
        form.notification_email.data = settings.notification_email
        form.email_notification.data = settings.email_notification

    return render_template('backup/settings.html',
                          title='Paramètres de Sauvegarde',
                          form=form,
                          settings=settings)


@login_required
def manual_backup():
    """Création d'une sauvegarde manuelle"""
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('dashboard'))

    form = ManualBackupForm()

    if form.validate_on_submit():
        import shutil
        import zipfile
        from datetime import datetime

        try:
            # Créer le dossier de sauvegardes s'il n'existe pas
            backup_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backups')
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            # Définir le nom du fichier
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if form.nom_fichier.data:
                filename = f"{form.nom_fichier.data}_{timestamp}"
            else:
                filename = f"backup_{timestamp}"

            # Chemin de la base de données
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.db')

            if form.compression.data:
                # Créer un fichier compressé
                backup_path = os.path.join(backup_folder, f"{filename}.zip")
                with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    zipf.write(db_path, 'app.db')
            else:
                # Copie directe
                backup_path = os.path.join(backup_folder, f"{filename}.db")
                shutil.copy2(db_path, backup_path)

            # Enregistrer la sauvegarde dans la base de données
            backup_log = BackupLog(
                nom_fichier=os.path.basename(backup_path),
                chemin_fichier=backup_path,
                taille_fichier=os.path.getsize(backup_path),
                type_backup='manual',
                statut='success',
                user_id=current_user.id
            )
            db.session.add(backup_log)
            db.session.commit()

            log_user_activity('create', 'backup', f'Création d\'une sauvegarde manuelle: {backup_log.nom_fichier}')
            flash(f'La sauvegarde a été créée avec succès: {backup_log.nom_fichier}', 'success')

        except Exception as e:
            # Enregistrer l'erreur
            backup_log = BackupLog(
                nom_fichier=filename,
                chemin_fichier='',
                type_backup='manual',
                statut='failed',
                message_erreur=str(e),
                user_id=current_user.id
            )
            db.session.add(backup_log)
            db.session.commit()

            flash(f'Échec de la création de la sauvegarde: {str(e)}', 'error')

        return redirect(url_for('backup_list'))

    return render_template('backup/manual.html',
                          title='Sauvegarde Manuelle',
                          form=form)


@login_required
def backup_list():
    """Liste des sauvegardes"""
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    backups = BackupLog.query.order_by(BackupLog.date_creation.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    log_user_activity('view', 'backup', 'Consultation de la liste des sauvegardes')

    return render_template('backup/list.html',
                          title='Sauvegardes',
                          backups=backups)


@login_required
def import_database():
    """Importation de la base de données"""
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('dashboard'))

    form = ImportDatabaseForm()

    if form.validate_on_submit():
        import shutil
        import zipfile

        try:
            # Sauvegarder le fichier téléchargé
            filename = secure_filename(form.fichier_backup.data.filename)
            upload_path = os.path.join(UPLOAD_FOLDER, 'temp', filename)

            # Créer un dossier temporaire
            temp_folder = os.path.join(UPLOAD_FOLDER, 'temp')
            if not os.path.exists(temp_folder):
                os.makedirs(temp_folder)

            form.fichier_backup.data.save(upload_path)

            # Chemin de la base de données actuelle
            current_db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.db')

            # Créer une sauvegarde de la base de données actuelle avant l'importation
            if not form.remplacer_donnees.data:
                backup_current = os.path.join(temp_folder, f'backup_before_import_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db')
                shutil.copy2(current_db_path, backup_current)

            # Traitement du fichier importé
            if filename.endswith('.zip'):
                # Extraire le fichier compressé
                with zipfile.ZipFile(upload_path, 'r') as zipf:
                    zipf.extractall(temp_folder)
                    # Rechercher le fichier de base de données
                    for file in zipf.namelist():
                        if file.endswith('.db') or file.endswith('.sqlite'):
                            extracted_db = os.path.join(temp_folder, file)
                            break
                    else:
                        raise Exception('Aucun fichier de base de données trouvé dans le fichier compressé')
            else:
                extracted_db = upload_path

            # Remplacer la base de données
            shutil.copy2(extracted_db, current_db_path)

            # Nettoyer les fichiers temporaires
            if os.path.exists(upload_path):
                os.remove(upload_path)
            if filename.endswith('.zip') and os.path.exists(extracted_db):
                os.remove(extracted_db)

            log_user_activity('create', 'backup', f'Importation de la base de données depuis: {filename}')
            flash('La base de données a été importée avec succès. Veuillez redémarrer l\'application.', 'success')

        except Exception as e:
            flash(f'Échec de l\'importation de la base de données: {str(e)}', 'error')

        return redirect(url_for('backup_list'))

    return render_template('backup/import.html',
                          title='Importation de la Base de Données',
                          form=form)


@login_required
def delete_backup(id):
    """Suppression d'une sauvegarde"""
    if not current_user.is_admin:
        flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'danger')
        return redirect(url_for('dashboard'))

    backup = BackupLog.query.get_or_404(id)

    try:
        # Supprimer le fichier physique s'il existe
        if backup.chemin_fichier and os.path.exists(backup.chemin_fichier):
            os.remove(backup.chemin_fichier)

        # Supprimer l'enregistrement de la base de données
        db.session.delete(backup)
        db.session.commit()

        log_user_activity('delete', 'backup', f'Suppression de la sauvegarde: {backup.nom_fichier}')
        flash('La sauvegarde a été supprimée avec succès.', 'success')

    except Exception as e:
        flash(f'Erreur lors de la suppression de la sauvegarde: {str(e)}', 'error')

    return redirect(url_for('backup_list'))
