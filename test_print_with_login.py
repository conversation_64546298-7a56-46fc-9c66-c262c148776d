#!/usr/bin/env python3
"""
Script de test pour vérifier les pages de print avec authentification
"""

import requests
import re

def test_print_with_login():
    session = requests.Session()
    
    print("🔐 Test des pages de print avec authentification")
    print("=" * 60)
    
    # Étape 1: Obtenir la page de connexion
    print("1. Récupération de la page de connexion...")
    login_page = session.get('http://127.0.0.1:5000/login')
    if login_page.status_code != 200:
        print(f"❌ Erreur lors de l'accès à la page de connexion: {login_page.status_code}")
        return
    
    # Étape 2: Extraire le token CSRF
    csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', login_page.text)
    if not csrf_match:
        print("❌ Token CSRF non trouvé")
        return
    
    csrf_token = csrf_match.group(1)
    print(f"✅ Token CSRF récupéré: {csrf_token[:20]}...")
    
    # Étape 3: Se connecter
    print("2. Connexion en cours...")
    login_data = {
        'username': 'admin',
        'password': 'admin123',
        'csrf_token': csrf_token
    }
    
    login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
    if login_response.status_code == 200 and 'dashboard' in login_response.url:
        print("✅ Connexion réussie")
    elif login_response.status_code == 302:
        print("✅ Connexion réussie (redirection)")
    else:
        print(f"❌ Échec de la connexion: {login_response.status_code}")
        return
    
    # Étape 4: Tester les pages de print
    print("3. Test des pages de print...")
    
    test_urls = [
        ('/organismes/print/1', 'Organisme'),
        ('/formateurs/print/1', 'Formateur'),
        ('/agenda/print/1', 'Agenda'),
        ('/rapports/print/1', 'Rapport'),
        ('/rapports/print/2', 'Rapport 2'),
    ]
    
    for url, name in test_urls:
        print(f"\n🔍 Test: {name}")
        print(f"URL: http://127.0.0.1:5000{url}")
        
        response = session.get(f'http://127.0.0.1:5000{url}')
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            # Vérifier la présence des éléments de l'entreprise
            logo_found = 'company-logo' in response.text
            footer_found = 'company-footer' in response.text
            company_info_found = 'company-info' in response.text
            company_name_found = 'مركز التكوين المهني' in response.text
            
            print(f"📷 Logo CSS class: {'✅' if logo_found else '❌'}")
            print(f"📄 Footer CSS class: {'✅' if footer_found else '❌'}")
            print(f"🏢 Company info CSS class: {'✅' if company_info_found else '❌'}")
            print(f"🏷️  Company name text: {'✅' if company_name_found else '❌'}")
            
            # Vérifier la structure HTML
            if 'print_layout.html' in response.text or 'company_info' in response.text:
                print("✅ Template de print utilisé correctement")
            else:
                print("⚠️  Template de print peut-être non utilisé")
                
        elif response.status_code == 404:
            print("⚠️  Données non trouvées (404)")
        elif response.status_code == 403:
            print("❌ Accès refusé (403)")
        else:
            print(f"❌ Erreur: {response.status_code}")

if __name__ == "__main__":
    test_print_with_login()
