# Gestion des Formations

Application web pour la gestion complète des formations, incluant les inscriptions, les dossiers techniques, les remboursements, les organismes, les formateurs et leurs agendas.

## Fonctionnalités

- Gestion des fiches d'inscription
- Gestion des dossiers techniques
- Gestion des dossiers de remboursement
- Gestion des organismes de formation
- Gestion des formateurs et de leurs agendas
- Gestion des domaines et thèmes de formation
- Système d'authentification et de gestion des utilisateurs
- Gestion des permissions par rôle
- Génération de rapports

## Installation

### Option 1: Installation via l'installateur (recommandé)

1. Téléchargez le fichier d'installation `Gestion_Formation_Setup.exe`
2. Exécutez l'installateur et suivez les instructions
3. L'application sera installée et un raccourci sera créé sur le bureau
4. Lancez l'application depuis le raccourci ou le menu Démarrer

### Option 2: Installation manuelle (pour les développeurs)

1. C<PERSON>z ce dépôt
2. Installez les dépendances:
   ```
   pip install -r requirements.txt
   ```
3. Initialisez la base de données:
   ```
   flask db upgrade
   ```
4. <PERSON>z l'application:
   ```
   python run.py
   ```

## Utilisation

1. Connectez-vous avec vos identifiants
2. Utilisez le tableau de bord pour accéder aux différentes fonctionnalités
3. Gérez les formations, les inscriptions, les dossiers techniques, etc.

## Accès réseau

L'application est accessible depuis n'importe quel appareil du réseau local à l'adresse:
```
http://IP_DE_VOTRE_PC:5000
```

## Création d'un fichier exécutable

Pour créer un fichier exécutable (.exe) et un installateur:

1. Exécutez le script `build_all.py`:
   ```
   python build_all.py
   ```

2. Ou suivez les instructions détaillées dans le fichier `README_BUILD.md`

## Configuration système requise

- Windows 7/8/10/11
- 4 Go de RAM minimum
- 500 Mo d'espace disque disponible

## Licence

Ce logiciel est protégé par le droit d'auteur. Tous droits réservés.
