import os

def search_in_files(directory, search_terms):
    results = {}
    for term in search_terms:
        results[term] = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py') or file.endswith('.html'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        for term in search_terms:
                            if term in content:
                                results[term].append((file_path, content.count(term)))
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return results

# البحث عن الإشارات إلى "accueil" و "formation"
search_terms = ['accueil', 'formation']
results = search_in_files('app', search_terms)

for term in search_terms:
    print(f"\nFiles containing '{term}':")
    for file_path, count in results[term]:
        print(f"{file_path}: {count} occurrences")
