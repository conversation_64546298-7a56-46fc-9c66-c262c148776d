#!/usr/bin/env python3
"""
Script de test pour vérifier que les pages de print affichent correctement 
le logo et les informations de pied de page de l'entreprise.
"""

import requests
import sys
from urllib.parse import urljoin

def test_print_page(base_url, endpoint, description):
    """Test une page de print spécifique"""
    url = urljoin(base_url, endpoint)
    print(f"\n🔍 Test: {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Vérifier la présence du logo
            logo_found = 'company-logo' in content or 'logo_path' in content
            
            # Vérifier la présence du pied de page
            footer_found = 'company-footer' in content or 'pied_de_page' in content
            
            # Vérifier la présence des informations de l'entreprise
            company_info_found = 'company-info' in content or 'nom_entreprise' in content
            
            print(f"✅ Page accessible (Status: {response.status_code})")
            print(f"📷 Logo détecté: {'✅' if logo_found else '❌'}")
            print(f"📄 Pied de page détecté: {'✅' if footer_found else '❌'}")
            print(f"🏢 Info entreprise détectée: {'✅' if company_info_found else '❌'}")
            
            return True
            
        elif response.status_code == 404:
            print(f"⚠️  Page non trouvée (404) - Probablement pas de données de test")
            return False
        elif response.status_code == 302:
            print(f"🔄 Redirection (302) - Authentification requise")
            return False
        else:
            print(f"❌ Erreur HTTP {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

def main():
    base_url = "http://127.0.0.1:5000"
    
    print("🚀 Test des pages de print avec logo et pied de page")
    print("=" * 60)
    
    # Test de connexion de base
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ Serveur accessible (Status: {response.status_code})")
    except requests.exceptions.RequestException as e:
        print(f"❌ Impossible de se connecter au serveur: {e}")
        print("Assurez-vous que le serveur Flask est en cours d'exécution")
        sys.exit(1)
    
    # Liste des pages de print à tester
    test_pages = [
        ("/print_fiche_inscription/1", "Fiche d'Inscription"),
        ("/print_dossier_technique/1", "Dossier Technique"),
        ("/print_organisme/1", "Organisme"),
        ("/print_formateur/1", "Formateur"),
        ("/print_agenda/1", "Agenda"),
        ("/print_remboursement/1", "Remboursement"),
        ("/print_rapport/1", "Rapport"),
    ]
    
    results = []
    for endpoint, description in test_pages:
        success = test_print_page(base_url, endpoint, description)
        results.append((description, success))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    for description, success in results:
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"{description:20} : {status}")
    
    print(f"\n🎯 Résultat global: {successful}/{total} pages testées avec succès")
    
    if successful == total:
        print("🎉 Tous les tests sont passés! Le logo et le pied de page devraient s'afficher correctement.")
    else:
        print("⚠️  Certains tests ont échoué. Vérifiez les données de test ou l'authentification.")

if __name__ == "__main__":
    main()
