import os
import shutil

# قائمة الملفات والمجلدات التي نريد حذفها
files_to_delete = [
    'app/templates/accueil.html',
    'app/templates/formation'
]

# حذف الملفات والمجلدات
for file_path in files_to_delete:
    if os.path.exists(file_path):
        if os.path.isdir(file_path):
            shutil.rmtree(file_path)
            print(f"تم حذف المجلد: {file_path}")
        else:
            os.remove(file_path)
            print(f"تم حذف الملف: {file_path}")
    else:
        print(f"الملف أو المجلد غير موجود: {file_path}")
