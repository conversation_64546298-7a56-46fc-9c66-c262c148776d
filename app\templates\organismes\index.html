{% extends "base.html" %}

{% block content %}
<div class="container">
    <style>
        .page-header {
            background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .page-header h1 {
            margin-bottom: 0;
            display: flex;
            align-items: center;
        }
        .page-header i {
            font-size: 2rem;
            margin-right: 15px;
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 50%;
        }
        .action-button {
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }
        .action-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }
        .search-box {
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            border-radius: 50px;
            overflow: hidden;
        }
        .search-box .form-control {
            border: none;
            padding: 15px 20px;
            border-radius: 50px 0 0 50px;
        }
        .search-box .btn {
            border-radius: 0 50px 50px 0;
            padding: 15px 25px;
        }
        .table-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .table th {
            background-color: #2c3e50;
            color: rgb(0, 0, 0);
            font-weight: 600;
            border: none;
        }
        .table td {
            vertical-align: middle;
        }
        .table tr:hover {
            background-color: rgba(44, 62, 80, 0.05);
        }
        .action-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 2px;
            transition: all 0.2s;
        }
        .action-btn:hover {
            transform: scale(1.1);
        }
    </style>

    <div class="card shadow mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0 d-flex align-items-center">
                    <i class="fas fa-building me-3" style="font-size: 1.2em;"></i>
                    <span style="font-weight: 600;">Organismes</span>
                </h4>
                <a href="{{ url_for('new_organisme') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus"></i> Nouvel Organisme
                </a>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_organismes') }}" method="GET">
                <div class="input-group search-box">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un organisme..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card table-container">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-list"></i> Liste des Organismes</h5>
        </div>
        <div class="card-body">
            {% if organismes %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Raison Sociale</th>
                                <th>Forme Juridique</th>
                                <th>Gérant</th>
                                <th>Ville</th>
                                <th>Téléphone</th>
                                <th>Fax</th>
                                <th>Patente</th>
                                <th>ID Fiscal</th>
                                <th>N° RC</th>
                                <th>Email</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for organisme in organismes %}
                                <tr>
                                    <td>{{ organisme.id }}</td>
                                    <td><strong>{{ organisme.raison_sociale }}</strong></td>
                                    <td>{{ organisme.forme_juridique }}</td>
                                    <td>{{ organisme.nom_prenom_gerant }}</td>
                                    <td>{{ organisme.ville }}</td>
                                    <td>{{ organisme.telephone }}</td>
                                    <td>{{ organisme.fax }}</td>
                                    <td>{{ organisme.patente }}</td>
                                    <td>{{ organisme.identifiant_fiscal }}</td>
                                    <td>{{ organisme.num_rc }}</td>
                                    <td>{{ organisme.email }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_organisme', id=organisme.id) }}" class="btn btn-warning action-btn" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('print_organisme', id=organisme.id) }}" class="btn btn-info action-btn" title="Imprimer" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger action-btn" title="Supprimer"
                                                    data-delete-url="{{ url_for('delete_organisme', id=organisme.id) }}"
                                                    data-delete-message="Êtes-vous sûr de vouloir supprimer cet organisme?"
                                                    data-use-modal="true"
                                                    data-delete-title="Confirmer la suppression">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun organisme trouvé. <a href="{{ url_for('new_organisme') }}" class="alert-link">Ajouter un nouvel organisme</a>.
                </div>
            {% endif %}
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='organismes') }}" class="btn btn-success action-button">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
