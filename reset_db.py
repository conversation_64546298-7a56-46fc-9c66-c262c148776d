from app import create_app, db
from app.models import User

app = create_app()

with app.app_context():
    # حذف جميع الجداول وإعادة إنشائها
    db.drop_all()
    db.create_all()
    
    # إنشاء مستخدم مسؤول
    admin = User(username='admin', email='<EMAIL>', is_admin=True)
    admin.set_password('admin123')  # كلمة مرور افتراضية
    db.session.add(admin)
    db.session.commit()
    
    print("تم إعادة تهيئة قاعدة البيانات وإنشاء مستخدم مسؤول")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")