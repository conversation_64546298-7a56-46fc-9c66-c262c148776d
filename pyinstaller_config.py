"""
Script pour faciliter la création du fichier exécutable avec PyInstaller.
Exécutez ce script avec Python pour créer le fichier exécutable.
"""

import os
import sys
import subprocess
import shutil

def main():
    print("===== Création du fichier exécutable pour Gestion des Formations =====")

    # Vérifier si PyInstaller est installé
    try:
        import PyInstaller
        print("PyInstaller est déjà installé.")
    except ImportError:
        print("Installation de PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

    # Installer les dépendances
    print("\nInstallation des dépendances...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    except subprocess.CalledProcessError as e:
        print(f"AVERTISSEMENT: Problème lors de l'installation des dépendances: {e}")
        print("Tentative d'installation des packages essentiels individuellement...")

        essential_packages = [
            "flask", "flask-login", "flask-sqlalchemy", "flask-wtf",
            "werkzeug", "jinja2", "itsdangerous", "sqlalchemy",
            "wtforms", "email_validator", "pyinstaller"
        ]

        for package in essential_packages:
            try:
                print(f"Installation de {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            except subprocess.CalledProcessError:
                print(f"AVERTISSEMENT: Impossible d'installer {package}, mais on continue...")

        print("Installation des dépendances terminée avec des avertissements.")

    # Créer le dossier dist s'il n'existe pas
    if not os.path.exists("dist"):
        os.makedirs("dist")

    # Créer le fichier exécutable
    print("\nCréation du fichier exécutable...")
    subprocess.check_call([
        "pyinstaller",
        "--noconfirm",
        "gestion_formation.spec"
    ])

    # Vérifier si le fichier exécutable a été créé
    if os.path.exists(os.path.join("dist", "Gestion_Formation.exe")):
        print("\nLe fichier exécutable a été créé avec succès!")
        print(f"Chemin: {os.path.abspath(os.path.join('dist', 'Gestion_Formation.exe'))}")
    else:
        print("\nERREUR: Le fichier exécutable n'a pas été créé.")
        return 1

    print("\n===== Processus terminé =====")
    return 0

if __name__ == "__main__":
    sys.exit(main())
