{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; border-radius: 10px 10px 0 0;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-chart-bar me-3" style="font-size: 1.2em;"></i>
                            <span style="font-weight: 600;">Rapports</span>
                        </h4>
                        <a href="{{ url_for('new_rapport') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Nouveau Rapport
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-body">
            {% if rapports %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="color: black;">ID</th>
                                <th style="color: black;">Titre</th>
                                <th style="color: black;">Type</th>
                                <th style="color: black;">Date de Création</th>
                                <th style="color: black;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rapport in rapports %}
                                <tr>
                                    <td>{{ rapport.id }}</td>
                                    <td>{{ rapport.titre }}</td>
                                    <td>{{ rapport.type_rapport }}</td>
                                    <td>{{ rapport.date_creation.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('view_rapport', id=rapport.id) }}" class="btn btn-sm btn-outline-primary" title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('print_rapport', id=rapport.id) }}" class="btn btn-sm btn-outline-success" title="Imprimer">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="Supprimer"
                                                    data-delete-url="{{ url_for('delete_rapport', id=rapport.id) }}"
                                                    data-delete-message="Êtes-vous sûr de vouloir supprimer ce rapport?"
                                                    data-use-modal="true"
                                                    data-delete-title="Confirmer la suppression">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    Aucun rapport trouvé. <a href="{{ url_for('new_rapport') }}">Créer un nouveau rapport</a>.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
