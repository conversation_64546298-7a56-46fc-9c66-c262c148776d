{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-user-edit"></i> Modifier Utilisateur</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations de l'Utilisateur</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.username.label(class="form-control-label") }}
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.email.label(class="form-control-label") }}
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.nom_complet.label(class="form-control-label") }}
                            {{ form.nom_complet(class="form-control") }}
                            {% if form.nom_complet.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nom_complet.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.password.label(class="form-control-label") }}
                            {{ form.password(class="form-control", placeholder="Laissez vide pour conserver le mot de passe actuel") }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Laissez vide pour conserver le mot de passe actuel</small>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            {{ form.is_admin(class="form-check-input") }}
                            {{ form.is_admin.label(class="form-check-label") }}
                            {% if form.is_admin.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_admin.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Permissions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    {{ form.perm_fiche_inscription(class="form-check-input") }}
                                    {{ form.perm_fiche_inscription.label(class="form-check-label") }}
                                    {% if form.perm_fiche_inscription.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.perm_fiche_inscription.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    {{ form.perm_dossier_technique(class="form-check-input") }}
                                    {{ form.perm_dossier_technique.label(class="form-check-label") }}
                                    {% if form.perm_dossier_technique.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.perm_dossier_technique.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    {{ form.perm_dossier_remboursement(class="form-check-input") }}
                                    {{ form.perm_dossier_remboursement.label(class="form-check-label") }}
                                    {% if form.perm_dossier_remboursement.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.perm_dossier_remboursement.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    {{ form.perm_organisme(class="form-check-input") }}
                                    {{ form.perm_organisme.label(class="form-check-label") }}
                                    {% if form.perm_organisme.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.perm_organisme.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    {{ form.perm_formateur(class="form-check-input") }}
                                    {{ form.perm_formateur.label(class="form-check-label") }}
                                    {% if form.perm_formateur.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.perm_formateur.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    {{ form.perm_agenda(class="form-check-input") }}
                                    {{ form.perm_agenda.label(class="form-check-label") }}
                                    {% if form.perm_agenda.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.perm_agenda.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    {{ form.perm_domaine_theme(class="form-check-input") }}
                                    {{ form.perm_domaine_theme.label(class="form-check-label") }}
                                    {% if form.perm_domaine_theme.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.perm_domaine_theme.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('users') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
