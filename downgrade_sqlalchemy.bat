@echo off
echo ===== Downgrade SQLAlchemy pour compatibilite avec PyInstaller =====
echo.

echo 1. Desinstallation de SQLAlchemy actuel...
pip uninstall -y sqlalchemy
echo.

echo 2. Installation de SQLAlchemy 1.4.46 (version stable)...
pip install sqlalchemy==1.4.46
echo.

echo 3. Creation du fichier executable...
pyinstaller --noconfirm --onedir --name Gestion_Formation run.py
echo.

echo 4. Verification du resultat...
if exist "dist\Gestion_Formation" (
    echo Le dossier executable a ete cree avec succes!
    echo Chemin: %CD%\dist\Gestion_Formation
) else (
    echo ERREUR: Le dossier executable n'a pas ete cree.
    exit /b 1
)
echo.

echo ===== Processus termine =====
pause
