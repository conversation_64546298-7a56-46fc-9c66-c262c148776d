"""
Script principal pour créer le fichier exécutable et l'installateur.
"""

import os
import sys
import subprocess
import importlib.util

def check_module_installed(module_name):
    """Vérifie si un module Python est installé"""
    return importlib.util.find_spec(module_name) is not None

def main():
    print("===== Assistant de création pour Gestion des Formations =====")

    # Étape 1: Créer le fichier exécutable
    print("\n[Étape 1/2] Création du fichier exécutable")
    if os.path.exists("pyinstaller_config.py"):
        try:
            result = subprocess.call([sys.executable, "pyinstaller_config.py"])
            if result != 0:
                print("ERREUR: La création du fichier exécutable a échoué.")
                print("Tentative de création directe avec PyInstaller...")
                try:
                    # Vérifier si PyInstaller est installé
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
                    # Exécuter PyInstaller directement
                    subprocess.check_call(["pyinstaller", "--noconfirm", "gestion_formation.spec"])
                    print("Création du fichier exécutable réussie via la méthode alternative!")
                except subprocess.CalledProcessError as e:
                    print(f"ERREUR: La création du fichier exécutable a échoué: {e}")
                    return 1
        except Exception as e:
            print(f"ERREUR: Exception lors de la création du fichier exécutable: {e}")
            return 1
    else:
        print("ERREUR: Le fichier pyinstaller_config.py n'existe pas.")
        return 1

    # Vérifier si le fichier exécutable a été créé
    if not os.path.exists(os.path.join("dist", "Gestion_Formation.exe")):
        print("ERREUR: Le fichier exécutable n'a pas été créé.")
        return 1

    # Étape 2: Créer l'installateur
    print("\n[Étape 2/2] Création de l'installateur")
    if os.path.exists("innosetup_config.py"):
        try:
            result = subprocess.call([sys.executable, "innosetup_config.py"])
            if result != 0:
                print("AVERTISSEMENT: La création de l'installateur a échoué.")
                print("Vous pouvez toujours utiliser le fichier exécutable directement.")
                print(f"Chemin: {os.path.abspath(os.path.join('dist', 'Gestion_Formation.exe'))}")
                # Ne pas retourner d'erreur ici, car l'exécutable est déjà créé
        except Exception as e:
            print(f"AVERTISSEMENT: Exception lors de la création de l'installateur: {e}")
            print("Vous pouvez toujours utiliser le fichier exécutable directement.")
            print(f"Chemin: {os.path.abspath(os.path.join('dist', 'Gestion_Formation.exe'))}")
    else:
        print("AVERTISSEMENT: Le fichier innosetup_config.py n'existe pas.")
        print("Vous pouvez toujours utiliser le fichier exécutable directement.")
        print(f"Chemin: {os.path.abspath(os.path.join('dist', 'Gestion_Formation.exe'))}")

    print("\n===== Processus terminé avec succès =====")
    print("\nVous pouvez maintenant distribuer l'installateur qui se trouve dans le dossier 'Output'.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
