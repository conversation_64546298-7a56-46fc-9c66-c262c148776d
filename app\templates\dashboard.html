{% extends "base.html" %}

{% block content %}
<!-- Enhanced Welcome Section -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card welcome-card text-white">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <div class="welcome-icon mr-4">
                            <i class="fas fa-tachometer-alt fa-3x"></i>
                        </div>
                        <div>
                            <h1 class="mb-2 font-weight-bold">Bienvenue, {{ current_user.username }}!</h1>
                            <p class="mb-0 lead">Tableau de bord - Système de gestion des formations</p>
                            <small class="text-light">Dernière connexion: <span id="last-login"></span></small>
                        </div>
                    </div>
                    <div class="welcome-stats text-right">
                        <div class="mb-2">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <span id="current-date"></span>
                        </div>
                        <div>
                            <i class="fas fa-clock mr-2"></i>
                            <span id="current-time"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .welcome-icon {
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        padding: 20px;
        backdrop-filter: blur(10px);
    }

    .stats-card {
        transition: all 0.3s ease;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        position: relative;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stats-card:hover::before {
        opacity: 1;
    }

    .stats-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card .card-body {
        position: relative;
        z-index: 2;
    }

    .stats-card i {
        opacity: 0.9;
        transition: all 0.3s ease;
    }

    .stats-card:hover i {
        opacity: 1;
        transform: scale(1.1);
    }

    .card-footer {
        background: rgba(0,0,0,0.1);
        border: none;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }

    .activity-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }

    .activity-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }

    .chart-container {
        position: relative;
        height: 300px;
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
</style>

<!-- Enhanced Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-gradient-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1 font-weight-bold">Fiches d'Inscription</h6>
                        <h2 class="mb-0 font-weight-bold">{{ stats.formations }}</h2>
                        <small class="text-light">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +12% ce mois
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-user-graduate fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link font-weight-bold" href="{{ url_for('fiches_inscription') }}">
                    <i class="fas fa-eye mr-1"></i>Voir Détails
                </a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-gradient-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1 font-weight-bold">Dossiers Techniques</h6>
                        <h2 class="mb-0 font-weight-bold">{{ stats.dossiers_techniques }}</h2>
                        <small class="text-light">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +8% ce mois
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-folder-open fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link font-weight-bold" href="{{ url_for('dossiers_techniques') }}">
                    <i class="fas fa-eye mr-1"></i>Voir Détails
                </a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-gradient-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1 font-weight-bold">Formateurs</h6>
                        <h2 class="mb-0 font-weight-bold">{{ stats.formateurs }}</h2>
                        <small class="text-light">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +5% ce mois
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-chalkboard-teacher fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link font-weight-bold" href="{{ url_for('formateurs') }}">
                    <i class="fas fa-eye mr-1"></i>Voir Détails
                </a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-gradient-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1 font-weight-bold">Remboursements</h6>
                        <h2 class="mb-0 font-weight-bold">{{ stats.dossiers_remboursement }}</h2>
                        <small class="text-light">
                            <i class="fas fa-arrow-down mr-1"></i>
                            -3% ce mois
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link font-weight-bold" href="{{ url_for('remboursements') }}">
                    <i class="fas fa-eye mr-1"></i>Voir Détails
                </a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-gradient-secondary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1 font-weight-bold">Organismes</h6>
                        <h2 class="mb-0 font-weight-bold">{{ stats.organismes }}</h2>
                        <small class="text-light">
                            <i class="fas fa-minus mr-1"></i>
                            Stable
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link font-weight-bold" href="{{ url_for('organismes') }}">
                    <i class="fas fa-eye mr-1"></i>Voir Détails
                </a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-gradient-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1 font-weight-bold">Utilisateurs</h6>
                        <h2 class="mb-0 font-weight-bold">{{ stats.users }}</h2>
                        <small class="text-light">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +2% ce mois
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-users-cog fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                {% if current_user.is_admin %}
                <a class="small text-white stretched-link font-weight-bold" href="{{ url_for('users') }}">
                    <i class="fas fa-eye mr-1"></i>Voir Détails
                </a>
                {% else %}
                <span class="small text-white">
                    <i class="fas fa-lock mr-1"></i>Accès restreint
                </span>
                {% endif %}
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <!-- Quick Actions Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card activity-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-plus-circle fa-3x text-primary"></i>
                </div>
                <h5 class="card-title font-weight-bold">Actions Rapides</h5>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('new_fiche_inscription') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-user-plus mr-1"></i>Nouvelle Fiche
                    </a>
                    <a href="{{ url_for('new_formateur') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-chalkboard-teacher mr-1"></i>Nouveau Formateur
                    </a>
                    <a href="{{ url_for('new_organisme') }}" class="btn btn-info btn-sm">
                        <i class="fas fa-building mr-1"></i>Nouvel Organisme
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card activity-card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-server fa-3x text-success"></i>
                </div>
                <h5 class="card-title font-weight-bold">État du Système</h5>
                <div class="mb-2">
                    <span class="badge badge-success">
                        <i class="fas fa-check-circle mr-1"></i>Opérationnel
                    </span>
                </div>
                <small class="text-muted">
                    <i class="fas fa-clock mr-1"></i>
                    Dernière sauvegarde: Aujourd'hui
                </small>
                <div class="mt-3">
                    {% if current_user.is_admin %}
                    <a href="{{ url_for('backup_list') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-database mr-1"></i>Sauvegardes
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics Section -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card activity-card">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 font-weight-bold">
                        <i class="fas fa-chart-line mr-2 text-primary"></i>
                        Évolution des Inscriptions
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary active">7 jours</button>
                        <button type="button" class="btn btn-outline-primary">30 jours</button>
                        <button type="button" class="btn btn-outline-primary">3 mois</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="inscriptionsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card activity-card h-100">
            <div class="card-header bg-white border-0">
                <h5 class="card-title mb-0 font-weight-bold">
                    <i class="fas fa-chart-pie mr-2 text-success"></i>
                    Répartition par Type
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="repartitionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities Section -->
<div class="row">
    <div class="col-lg-6">
        <div class="card activity-card">
            <div class="card-header bg-white border-0">
                <h5 class="card-title mb-0 font-weight-bold">
                    <i class="fas fa-history mr-2 text-info"></i>
                    Activités Récentes
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    {% if recent_activities %}
                        {% for activity in recent_activities %}
                        <div class="timeline-item">
                            <div class="timeline-marker
                                {% if activity.action_type == 'create' %}bg-primary
                                {% elif activity.action_type == 'update' %}bg-warning
                                {% elif activity.action_type == 'delete' %}bg-danger
                                {% else %}bg-info{% endif %}"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{{ activity.action_type|title }} {{ activity.object_type|title }}</h6>
                                <p class="timeline-text">{{ activity.description }}</p>
                                <small class="text-muted">
                                    {{ activity.timestamp.strftime('%d/%m/%Y à %H:%M') }} par {{ activity.user.username }}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-secondary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Aucune activité récente</h6>
                                <p class="timeline-text">Commencez à utiliser le système pour voir les activités ici</p>
                                <small class="text-muted">Système prêt</small>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <div class="text-center mt-3">
                    {% if current_user.is_admin %}
                    <a href="{{ url_for('activity_log') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list mr-1"></i>Voir toutes les activités
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card activity-card">
            <div class="card-header bg-white border-0">
                <h5 class="card-title mb-0 font-weight-bold">
                    <i class="fas fa-calendar-check mr-2 text-warning"></i>
                    Agenda du Jour
                </h5>
            </div>
            <div class="card-body">
                <div class="agenda-list">
                    {% if today_agenda %}
                        {% for agenda in today_agenda %}
                        <div class="agenda-item d-flex align-items-center mb-3">
                            <div class="agenda-time text-white" style="background-color: {{ agenda.formateur.couleur_fond or '#007bff' }};">
                                {{ agenda.date_debut.strftime('%H:%M') }}
                            </div>
                            <div class="agenda-content ml-3">
                                <h6 class="mb-1">{{ agenda.description }}</h6>
                                <small class="text-muted">Formateur: {{ agenda.formateur.nom_prenom }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="agenda-item d-flex align-items-center mb-3">
                            <div class="agenda-time bg-secondary text-white">
                                --:--
                            </div>
                            <div class="agenda-content ml-3">
                                <h6 class="mb-1">Aucun événement aujourd'hui</h6>
                                <small class="text-muted">Agenda libre</small>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('agenda_formateurs') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-calendar mr-1"></i>Voir l'agenda complet
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Enhanced Features -->
<script>
// Update current time and date
function updateDateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('fr-FR');
    const dateString = now.toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    document.getElementById('current-time').textContent = timeString;
    document.getElementById('current-date').textContent = dateString;

    // Set last login (mock data for now)
    const lastLogin = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 hours ago
    document.getElementById('last-login').textContent = lastLogin.toLocaleDateString('fr-FR') + ' à ' + lastLogin.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
}

// Update time every second
setInterval(updateDateTime, 1000);
updateDateTime();

// Chart.js configurations
document.addEventListener('DOMContentLoaded', function() {
    // Inscriptions Chart
    const inscriptionsCtx = document.getElementById('inscriptionsChart');
    if (inscriptionsCtx) {
        new Chart(inscriptionsCtx, {
            type: 'line',
            data: {
                labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                datasets: [{
                    label: 'Inscriptions',
                    data: [12, 19, 8, 15, 22, 13, 18],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Repartition Chart
    const repartitionCtx = document.getElementById('repartitionChart');
    if (repartitionCtx) {
        new Chart(repartitionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Fiches', 'Dossiers', 'Formateurs', 'Organismes'],
                datasets: [{
                    data: [{{ stats.formations }}, {{ stats.dossiers_techniques }}, {{ stats.formateurs }}, {{ stats.organismes }}],
                    backgroundColor: [
                        '#667eea',
                        '#56ab2f',
                        '#3498db',
                        '#95a5a6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
});
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

.timeline-title {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    color: #6c757d;
}

.agenda-time {
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 0.9rem;
    min-width: 60px;
    text-align: center;
}

.agenda-content h6 {
    font-weight: 600;
    color: #495057;
}

.stats-icon {
    opacity: 0.8;
}
</style>

{% endblock %}
