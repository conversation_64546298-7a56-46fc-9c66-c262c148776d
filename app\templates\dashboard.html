{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="mr-3">
                        <i class="fas fa-tachometer-alt fa-3x"></i>
                    </div>
                    <div>
                        <h1 class="mb-2">Bienvenue, {{ current_user.username }}!</h1>
                        <p class="mb-0">Voici un aperçu de votre système de gestion des formations.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }
    .stats-card {
        transition: all 0.3s;
    }
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .stats-card i {
        opacity: 0.8;
    }
    .stats-card:hover i {
        opacity: 1;
    }
</style>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-primary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">Fiches d'Inscription</h6>
                        <h2 class="mb-0">{{ stats.formations }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('fiches_inscription') }}">Voir Détails</a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">Dossiers Techniques</h6>
                        <h2 class="mb-0">{{ stats.dossiers_techniques }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('dossiers_techniques') }}">Voir Détails</a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-info text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">Formateurs</h6>
                        <h2 class="mb-0">{{ stats.formateurs }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-chalkboard-teacher fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('formateurs') }}">Voir Détails</a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">Remboursements</h6>
                        <h2 class="mb-0">{{ stats.dossiers_remboursement }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('remboursements') }}">Voir Détails</a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics -->
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-secondary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">Organismes</h6>
                        <h2 class="mb-0">{{ stats.organismes }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="{{ url_for('organismes') }}">Voir Détails</a>
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card stats-card bg-danger text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">Utilisateurs</h6>
                        <h2 class="mb-0">{{ stats.users }}</h2>
                    </div>
                    <div>
                        <i class="fas fa-users-cog fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                {% if current_user.is_admin %}
                <a class="small text-white stretched-link" href="{{ url_for('users') }}">Voir Détails</a>
                {% else %}
                <span class="small text-white">Accès restreint</span>
                {% endif %}
                <i class="fas fa-angle-right"></i>
            </div>
        </div>
    </div>
</div>


{% endblock %}
