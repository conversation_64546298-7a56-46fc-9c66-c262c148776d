from app import create_app, db
from app.models import Organisme

def check_organismes():
    app = create_app()
    with app.app_context():
        organismes = Organisme.query.all()
        print('Organismes:')
        for organisme in organismes:
            print(f'ID: {organisme.id}, Raison Sociale: {organisme.raison_sociale}, Forme Juridique: {organisme.forme_juridique}')
            print(f'Gérant: {organisme.nom_prenom_gerant}, Ville: {organisme.ville}, Téléphone: {organisme.telephone}, Email: {organisme.email}')

if __name__ == '__main__':
    check_organismes()
