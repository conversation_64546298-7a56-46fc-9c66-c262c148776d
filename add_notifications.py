from app import create_app, db
from app.models import Notification, User
from datetime import datetime, timezone, timedelta

def add_notifications():
    app = create_app()
    with app.app_context():
        # إضافة إشعارات
        if Notification.query.count() == 0:
            users = User.query.all()
            
            if users:
                notification1 = Notification(
                    message="Bienvenue dans le système de gestion des formations!",
                    date_creation=datetime.now(timezone.utc),
                    lu=False,
                    user_id=users[0].id
                )
                
                notification2 = Notification(
                    message="Nouvelle formation ajoutée: Développement Web",
                    date_creation=datetime.now(timezone.utc) - timedelta(hours=2),
                    lu=False,
                    user_id=users[0].id
                )
                
                notification3 = Notification(
                    message="Rappel: Validation des dossiers en attente",
                    date_creation=datetime.now(timezone.utc) - timedelta(days=1),
                    lu=True,
                    user_id=users[0].id
                )
                
                db.session.add_all([notification1, notification2, notification3])
                db.session.commit()
                print("Added sample notifications")
            else:
                print("No users found")
        
        print("Sample notifications added successfully!")

if __name__ == '__main__':
    add_notifications()
